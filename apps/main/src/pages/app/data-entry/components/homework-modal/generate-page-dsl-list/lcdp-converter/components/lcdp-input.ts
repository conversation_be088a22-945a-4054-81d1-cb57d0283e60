import { Converter, DslData, AthComponentType, DslSchema, ConvertOutput } from '../type';

const InputTitleType = {
  string: '文本输入',
  numeric: '数字输入',
};

export const LcdpInputConverter: Converter = {
  key: AthComponentType.INPUT,
  toSchema: (dsl: DslData): ConvertOutput<DslSchema, DslData> => {
    // 通用转换逻辑，没有子层级
    // 将dsl数据 塞入 dslInfo 中
    return {
      data: {
        // 如果dsl中type存在，且物料中有渲染该type的组件那么就正常渲染，否则使用COMMON组件渲染，现在的COMMON组件就是之前的自定义组件
        title: InputTitleType[dsl?.dataType] ?? '文本输入',
        componentName: dsl.type ?? AthComponentType.COMMON,
        props: { dslInfo: { ...dsl, enableAuthKey: !!dsl.authKey } },
      },
      childrenData: [],
    };
  },

  toDsl: (dslSchema: DslSchema): ConvertOutput<DslData, DslSchema> => {
    // 通用转换逻辑，将dslInfo取出，作为当前组件的 dsl 数据
    // 不会像其他的转换器直接赋值type，仅透传dslInfo
    const dslInfo = dslSchema?.props?.dslInfo;
    delete dslInfo.enableAuthKey;
    return {
      data: { ...dslInfo },
      childrenData: [],
    };
  },

  valid: (dsl: DslData) => {
    return true;
  },
};
