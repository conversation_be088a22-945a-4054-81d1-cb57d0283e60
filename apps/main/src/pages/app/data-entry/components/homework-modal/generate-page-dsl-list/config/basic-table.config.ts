export const basicTableDslList = [
  {
    type: 'browse',
    dsl: {
      layout: [
        {
          schema: 'receipt_checking_no_transfer',
          isSort: true,
          saveColumnsWidth: false,
          suppressAutoAddRow: false,
          headerName: '银企直连接收对账信息',
          rowDelete: false,
          adaptiveModel: 'default',
          type: 'ATHENA_TABLE',
          setting: {
            hideDefaultToolbar: [],
            options: [],
          },
          path: '',
          isBaseData: true,
          rowSpanTree: false,
          checkbox: true,
          rowIndex: false,
          id: '15b33sfsr324242342342342348cca4',
          lang: {
            tableTitle: {
              zh_CN: '',
              en_US: '',
              zh_TW: '',
            },
            headerName: {
              zh_CN: '银企直连接收对账信息',
              zh_TW: '银企直连接收对账信息',
              en_US: 'receipt_checking',
            },
          },
          tableTitle: '',
          queryInfo: {
            pageInfo: {
              pageSize: 10,
              pageNo: 1,
              totalResults: 10,
              hasNext: true,
            },
            dataFilter: '',
            isAsync: true,
            dataSourceType: 'dataConnector',
            dataConnectorId: 'receipt_checking_no_transfer',
          },
          rowHeight: 100,
          slots: [
            {
              select: 'slot-top-right',
              height: 140,
              group: [
                {
                  headerName: '按钮组',
                  gap: '12px',
                  block: true,
                  id: 'cb120bc8-2360-4294-bfbf-0a0f2bfb7096',
                  type: 'BUTTON_GROUP',
                  lang: {
                    headerName: {
                      zh_CN: '按钮组',
                      zh_TW: '按鈕組',
                      en_US: 'Button Group',
                    },
                  },
                  moreButtonConfig: {
                    enable: false,
                  },
                  verticalAlignment: 'center',
                  justifyContent: 'flex-start',
                  group: [
                    {
                      id: '48cfac84-adf1-4eda-b233-34d215f1a678',
                      type: 'BUTTON_OPENPAGE_ADD_DECOUPLE',
                      title: '新增',
                      lang: {
                        title: {
                          zh_CN: '新增',
                          zh_TW: '新增',
                          en_US: 'Create',
                        },
                      },
                      styleMode: 'primary',
                      size: 'small',
                      disabled: false,
                      ghost: false,
                      danger: false,
                      block: false,
                      debounce: false,
                      showLoading: false,
                      async: false,
                      debounceTime: 300,
                      targetSchema: 'receipt_checking_no_transfer',
                      targetPath: '',
                      emitConfig: {
                        operationType: 'open_page_add',
                        dslCode: 'DSL_b08c214210000bb2',
                      },
                    },
                    {
                      id: '7bceea7e-c7da-4e6f-8ab2-ebe6933efee1',
                      type: 'BUTTON_DATA_DELETE_DECOUPLE',
                      title: '删除',
                      lang: {
                        title: {
                          zh_CN: '删除',
                          zh_TW: '删除',
                          en_US: 'Delete',
                        },
                      },
                      styleMode: 'default',
                      size: 'small',
                      disabled: false,
                      ghost: false,
                      danger: false,
                      block: false,
                      debounce: false,
                      showLoading: false,
                      async: false,
                      debounceTime: 300,
                      trailingAction: 'reload-page',
                      targetSchema: 'receipt_checking_no_transfer',
                      targetPath: '',
                      action: {
                        submitType: {
                          schema: '',
                          isBatch: false,
                        },
                        trackCode: 'SUBMIT',
                        dataSourceType: 'dataConnector',
                        dataConnectorId: 'receipt_checking_delete',
                        type: 'ESP',
                        actionType: 'basic-data-delete',
                      },
                    },
                  ],
                },
              ],
            },
          ],
          columnDefs: [
            {
              headerName: '抛转状态',
              width: 100,
              path: '',
              level: 0,
              lang: {
                headerName: {
                  zh_CN: '抛转状态',
                  zh_TW: '拋轉狀態',
                  en_US: 'generate_status',
                },
              },
              id: '79805f28-a0f0-47f6-916f-fdc91a8a9426',
              columns: [
                {
                  id: '6348d595-39fb-49f5-bc3a-999fe14d4e91',
                  type: 'SELECT',
                  headerName: '抛转状态',
                  placeholder: '请选择',
                  schema: 'generate_status',
                  path: 'receipt_checking_no_transfer',
                  disabled: false,
                  editable: true,
                  isFocusDisplay: false,
                  dataType: 'string',
                  tooltipMode: 'normal',
                  iconType: '',
                  options: [
                    {
                      lang: {
                        title: {
                          en_US: '已抛转',
                          zh_CN: '已抛转',
                          zh_TW: '已抛转',
                        },
                      },
                      value: '1',
                      title: '已抛转',
                    },
                    {
                      lang: {
                        title: {
                          en_US: '未抛转',
                          zh_CN: '未抛转',
                          zh_TW: '未抛转',
                        },
                      },
                      value: '2',
                      title: '未抛转',
                    },
                  ],
                  lang: {
                    headerName: {
                      zh_CN: '抛转状态',
                      zh_TW: '拋轉狀態',
                      en_US: 'generate_status',
                    },
                    placeholder: {
                      zh_CN: '请选择',
                      zh_TW: '請選擇',
                      en_US: 'please select',
                    },
                    extraContent: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                    tooltipTitle: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                  },
                  dataConnectorId: '',
                  optionsType: 'dataConnector',
                },
              ],
            },
            {
              headerName: '数据源',
              width: 100,
              path: '',
              level: 0,
              lang: {
                headerName: {
                  zh_CN: '数据源',
                  zh_TW: '数据源',
                  en_US: 'data_source',
                },
              },
              id: '02b6bdbd-ae98-400c-b1a5-c024821f8133',
              columns: [
                {
                  id: '960b447e-943b-490e-bf0e-aef93906230c',
                  type: 'SELECT',
                  headerName: '数据源',
                  placeholder: '请选择',
                  schema: 'data_source',
                  path: 'receipt_checking_no_transfer',
                  disabled: false,
                  editable: true,
                  isFocusDisplay: false,
                  dataType: 'string',
                  tooltipMode: 'normal',
                  iconType: '',
                  options: [
                    {
                      lang: {
                        title: {
                          en_US: '手动导入',
                          zh_CN: '手动导入',
                          zh_TW: '手动导入',
                        },
                      },
                      value: '0',
                      title: '手动导入',
                    },
                    {
                      lang: {
                        title: {
                          en_US: '银企直联导入',
                          zh_CN: '银企直联导入',
                          zh_TW: '银企直联导入',
                        },
                      },
                      value: '1',
                      title: '银企直联导入',
                    },
                  ],
                  lang: {
                    headerName: {
                      zh_CN: '数据源',
                      zh_TW: '数据源',
                      en_US: 'data_source',
                    },
                    placeholder: {
                      zh_CN: '请选择',
                      zh_TW: '請選擇',
                      en_US: 'please select',
                    },
                    extraContent: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                    tooltipTitle: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                  },
                  dataConnectorId: '',
                  optionsType: 'dataConnector',
                },
              ],
            },
            {
              headerName: '银行交易账户说明',
              width: 160,
              path: '',
              level: 0,
              lang: {
                headerName: {
                  zh_CN: '银行交易账户说明',
                  zh_TW: '银行交易账户说明',
                  en_US: 'bank_trans_name',
                },
              },
              id: 'a2c63279-6dfd-4734-85c6-50c600e9eade',
              columns: [
                {
                  id: '46333e00-713b-4566-a518-5c9895d8df3c',
                  type: 'INPUT',
                  headerName: '资金中心',
                  placeholder: '请输入',
                  schema: 'cashing_center',
                  path: 'receipt_checking_no_transfer',
                  disabled: false,
                  editable: true,
                  isFocusDisplay: false,
                  dataType: 'string',
                  tooltipMode: 'normal',
                  iconType: '',
                  lang: {
                    headerName: {
                      zh_CN: '资金中心',
                      zh_TW: '资金中心',
                      en_US: 'cashing_center',
                    },
                    placeholder: {
                      zh_CN: '请输入',
                      zh_TW: '請輸入',
                      en_US: 'please enter',
                    },
                    extraContent: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                    tooltipTitle: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                  },
                },
                {
                  id: '656e618b-16ef-40a4-bbb6-a40566531b24',
                  type: 'INPUT',
                  headerName: '资金中心说明',
                  placeholder: '请输入',
                  schema: 'cashing_center_name',
                  path: 'receipt_checking_no_transfer',
                  disabled: false,
                  editable: true,
                  isFocusDisplay: false,
                  dataType: 'string',
                  tooltipMode: 'normal',
                  iconType: '',
                  lang: {
                    headerName: {
                      zh_CN: '资金中心说明',
                      zh_TW: '资金中心说明',
                      en_US: 'cashing_center_name',
                    },
                    placeholder: {
                      zh_CN: '请输入',
                      zh_TW: '請輸入',
                      en_US: 'please enter',
                    },
                    extraContent: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                    tooltipTitle: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                  },
                },
                {
                  id: 'f438767b-b7be-4e17-83f4-9aa10535356d',
                  type: 'INPUT',
                  headerName: '银行交易账户编码',
                  placeholder: '请输入',
                  schema: 'bank_trans_code',
                  path: 'receipt_checking_no_transfer',
                  disabled: false,
                  editable: true,
                  isFocusDisplay: false,
                  dataType: 'string',
                  tooltipMode: 'normal',
                  iconType: '',
                  lang: {
                    headerName: {
                      zh_CN: '银行交易账户编码',
                      zh_TW: '银行交易账户编码',
                      en_US: 'bank_trans_code',
                    },
                    placeholder: {
                      zh_CN: '请输入',
                      zh_TW: '請輸入',
                      en_US: 'please enter',
                    },
                    extraContent: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                    tooltipTitle: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                  },
                },
                {
                  id: 'b425e85c-f268-4da1-8400-cdd7cdf2a9e2',
                  type: 'INPUT',
                  headerName: '银行交易账户说明',
                  placeholder: '请输入',
                  schema: 'bank_trans_name',
                  path: 'receipt_checking_no_transfer',
                  disabled: false,
                  editable: true,
                  isFocusDisplay: false,
                  dataType: 'string',
                  tooltipMode: 'normal',
                  iconType: '',
                  lang: {
                    headerName: {
                      zh_CN: '银行交易账户说明',
                      zh_TW: '银行交易账户说明',
                      en_US: 'bank_trans_name',
                    },
                    placeholder: {
                      zh_CN: '请输入',
                      zh_TW: '請輸入',
                      en_US: 'please enter',
                    },
                    extraContent: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                    tooltipTitle: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                  },
                },
              ],
            },
            {
              headerName: '银行对账单信息',
              width: 160,
              path: '',
              level: 0,
              lang: {
                headerName: {
                  zh_CN: '银行对账单信息',
                  en_US: '',
                  zh_TW: '',
                },
              },
              id: '29dceabe-bf23-4466-aa8f-b17dd577ea93',
              columns: [
                {
                  id: 'f05f64d4-bc66-40ad-9558-c0e13b043d32',
                  type: 'DATEPICKER',
                  headerName: '交易日期',
                  placeholder: 'yyyyMMdd ',
                  schema: 'trade_date',
                  path: 'receipt_checking_no_transfer',
                  disabled: false,
                  editable: true,
                  isFocusDisplay: false,
                  fieldType: 'datetime',
                  dataType: 'date',
                  tooltipMode: 'normal',
                  iconType: 'wenhao',
                  weekStartsOn: 1,
                  mode: 'date',
                  format: 'yyyy/MM/dd',
                  formatConfig: {
                    type: 'yyyy/MM/dd',
                    value: 'yyyy/MM/dd',
                  },
                  showPickerOptions: false,
                  pickerOptions: [],
                  disabledDate: {
                    type: 'none',
                  },
                  rules: {
                    value: [],
                  },
                  lang: {
                    headerName: {
                      zh_CN: '交易日期',
                      zh_TW: '交易日期',
                      en_US: 'trade_date',
                    },
                    placeholder: {
                      zh_CN: 'yyyyMMdd',
                      zh_TW: 'yyyyMMdd',
                      en_US: 'yyyyMMdd',
                    },
                    extraContent: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                    tooltipTitle: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                  },
                },
                {
                  id: '912b2b4e-0efb-48f1-a67f-7bf7e07fe58c',
                  type: 'INPUT',
                  headerName: '对方账户名',
                  placeholder: '请输入',
                  schema: 'opponent_account_name',
                  path: 'receipt_checking_no_transfer',
                  disabled: false,
                  editable: true,
                  isFocusDisplay: false,
                  dataType: 'string',
                  tooltipMode: 'normal',
                  iconType: '',
                  lang: {
                    headerName: {
                      zh_CN: '对方账户名',
                      zh_TW: '对方账户名',
                      en_US: 'opponent_account_name',
                    },
                    placeholder: {
                      zh_CN: '请输入',
                      zh_TW: '請輸入',
                      en_US: 'please enter',
                    },
                    extraContent: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                    tooltipTitle: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                  },
                },
                {
                  id: 'b2143ccb-e000-4d8f-bb42-7fd694d598f0',
                  type: 'INPUT',
                  headerName: '对方账号',
                  placeholder: '请输入',
                  schema: 'opponent_account',
                  path: 'receipt_checking_no_transfer',
                  disabled: false,
                  editable: true,
                  isFocusDisplay: false,
                  dataType: 'string',
                  tooltipMode: 'normal',
                  iconType: '',
                  lang: {
                    headerName: {
                      zh_CN: '对方账号',
                      zh_TW: '对方账号',
                      en_US: 'opponent_account',
                    },
                    placeholder: {
                      zh_CN: '请输入',
                      zh_TW: '請輸入',
                      en_US: 'please enter',
                    },
                    extraContent: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                    tooltipTitle: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                  },
                },
              ],
            },
            {
              headerName: '企业流水号',
              width: 100,
              path: '',
              level: 0,
              lang: {
                headerName: {
                  zh_CN: '企业流水号',
                  zh_TW: '企业流水号',
                  en_US: 'serial_no',
                },
              },
              id: 'd7fb958d-cc21-4990-9e4e-8e4f494176bf',
              columns: [
                {
                  id: '81fb9b29-20f4-45f5-920c-8047fab69fcd',
                  type: 'INPUT',
                  headerName: '企业流水号',
                  placeholder: '请输入',
                  schema: 'serial_no',
                  path: 'receipt_checking_no_transfer',
                  disabled: false,
                  editable: true,
                  isFocusDisplay: false,
                  dataType: 'string',
                  tooltipMode: 'normal',
                  iconType: '',
                  lang: {
                    headerName: {
                      zh_CN: '企业流水号',
                      zh_TW: '企业流水号',
                      en_US: 'serial_no',
                    },
                    placeholder: {
                      zh_CN: '请输入',
                      zh_TW: '請輸入',
                      en_US: 'please enter',
                    },
                    extraContent: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                    tooltipTitle: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                  },
                },
              ],
            },
            {
              headerName: '附言',
              width: 100,
              path: '',
              level: 0,
              lang: {
                headerName: {
                  zh_CN: '附言',
                  zh_TW: '附言',
                  en_US: 'postscript',
                },
              },
              id: '5862f885-3caf-4ab3-be27-36f5a5f1b3be',
              columns: [
                {
                  id: '67c180a7-e1d4-4426-86bc-07ff47a53e01',
                  type: 'INPUT',
                  headerName: '用途',
                  placeholder: '请输入',
                  schema: 'usage',
                  path: 'receipt_checking_no_transfer',
                  disabled: false,
                  editable: true,
                  isFocusDisplay: false,
                  dataType: 'string',
                  tooltipMode: 'normal',
                  iconType: '',
                  lang: {
                    headerName: {
                      zh_CN: '用途',
                      zh_TW: '用途',
                      en_US: 'usage',
                    },
                    placeholder: {
                      zh_CN: '请输入',
                      zh_TW: '請輸入',
                      en_US: 'please enter',
                    },
                    extraContent: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                    tooltipTitle: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                  },
                },
                {
                  id: '6686587f-3847-45d6-ae2c-b6d3e59e8432',
                  type: 'INPUT',
                  headerName: '附言',
                  placeholder: '请输入',
                  schema: 'postscript',
                  path: 'receipt_checking_no_transfer',
                  disabled: false,
                  editable: true,
                  isFocusDisplay: false,
                  dataType: 'string',
                  tooltipMode: 'normal',
                  iconType: '',
                  lang: {
                    headerName: {
                      zh_CN: '附言',
                      zh_TW: '附言',
                      en_US: 'postscript',
                    },
                    placeholder: {
                      zh_CN: '请输入',
                      zh_TW: '請輸入',
                      en_US: 'please enter',
                    },
                    extraContent: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                    tooltipTitle: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                  },
                },
              ],
            },
            {
              headerName: '交易金额',
              width: 100,
              path: '',
              level: 0,
              lang: {
                headerName: {
                  zh_CN: '交易金额',
                  zh_TW: '交易金额',
                  en_US: 'trans_amount',
                },
              },
              id: '191e2041-817b-4661-aab2-51536788154b',
              columns: [
                {
                  id: 'dac2de09-16a1-4f5b-ae27-2081826cb190',
                  type: 'INPUT',
                  headerName: '交易金额',
                  placeholder: '请输入',
                  schema: 'trans_amount',
                  path: 'receipt_checking_no_transfer',
                  disabled: false,
                  editable: true,
                  isFocusDisplay: false,
                  dataType: 'numeric',
                  tooltipMode: 'normal',
                  iconType: '',
                  dataPrecision: {
                    length: '',
                    place: '',
                  },
                  max: '',
                  min: '',
                  step: 1,
                  lang: {
                    headerName: {
                      zh_CN: '交易金额',
                      zh_TW: '交易金额',
                      en_US: 'trans_amount',
                    },
                    placeholder: {
                      zh_CN: '请输入',
                      zh_TW: '請輸入',
                      en_US: 'please enter',
                    },
                    extraContent: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                    tooltipTitle: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                  },
                },
              ],
            },
            {
              headerName: '交易性质',
              width: 100,
              path: '',
              level: 0,
              lang: {
                headerName: {
                  zh_CN: '交易性质',
                  zh_TW: '交易性质',
                  en_US: 'trans_property',
                },
              },
              id: '39518365-4425-4c4e-ae1c-5bae931c8abc',
              columns: [
                {
                  id: 'd092c7b0-9863-40a6-b4e1-dccf9c9f87d1',
                  type: 'SELECT',
                  headerName: '交易性质',
                  placeholder: '请选择',
                  schema: 'trans_property',
                  path: 'receipt_checking_no_transfer',
                  disabled: false,
                  editable: true,
                  isFocusDisplay: false,
                  dataType: 'string',
                  tooltipMode: 'normal',
                  iconType: '',
                  options: [
                    {
                      lang: {
                        title: {
                          en_US: '客户收款',
                          zh_CN: '客户收款',
                          zh_TW: '客户收款',
                        },
                      },
                      value: '0',
                      title: '客户收款',
                    },
                    {
                      lang: {
                        title: {
                          en_US: '账户间转账',
                          zh_CN: '账户间转账',
                          zh_TW: '账户间转账',
                        },
                      },
                      value: '5',
                      title: '账户间转账',
                    },
                  ],
                  lang: {
                    headerName: {
                      zh_CN: '交易性质',
                      zh_TW: '交易性质',
                      en_US: 'trans_property',
                    },
                    placeholder: {
                      zh_CN: '请选择',
                      zh_TW: '請選擇',
                      en_US: 'please select',
                    },
                    extraContent: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                    tooltipTitle: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                  },
                  dataConnectorId: '',
                  optionsType: 'dataConnector',
                },
              ],
            },
            {
              headerName: '交易对象名称',
              width: 100,
              path: '',
              level: 0,
              lang: {
                headerName: {
                  zh_CN: '交易对象名称',
                  zh_TW: '交易对象名称',
                  en_US: 'trading_partner_name',
                },
              },
              id: '357fb46f-474b-4f27-97c0-45c0a83d8e56',
              columns: [
                {
                  id: '9c6cf22a-5b18-4289-aec3-dac9044f4153',
                  type: 'INPUT',
                  headerName: '交易对象编号',
                  placeholder: '请选择',
                  schema: 'trading_partner',
                  path: 'receipt_checking_no_transfer',
                  disabled: false,
                  editable: true,
                  isFocusDisplay: false,
                  dataType: 'string',
                  tooltipMode: 'normal',
                  iconType: '',
                  lang: {
                    headerName: {
                      zh_CN: '交易对象编号',
                      zh_TW: '交易对象编号',
                      en_US: 'trading_partner',
                    },
                    placeholder: {
                      zh_CN: '请选择',
                      zh_TW: '請選擇',
                      en_US: 'please select',
                    },
                    extraContent: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                    tooltipTitle: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                  },
                },
                {
                  id: 'd486ba71-de07-4508-a6eb-4e4d590fca96',
                  type: 'INPUT',
                  headerName: '交易对象名称',
                  placeholder: '请输入',
                  schema: 'trading_partner_name',
                  path: 'receipt_checking_no_transfer',
                  disabled: false,
                  editable: true,
                  isFocusDisplay: false,
                  dataType: 'string',
                  tooltipMode: 'normal',
                  iconType: '',
                  lang: {
                    headerName: {
                      zh_CN: '交易对象名称',
                      zh_TW: '交易对象名称',
                      en_US: 'trading_partner_name',
                    },
                    placeholder: {
                      zh_CN: '请输入',
                      zh_TW: '請輸入',
                      en_US: 'please enter',
                    },
                    extraContent: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                    tooltipTitle: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                  },
                },
              ],
            },
            {
              headerName: '存提码说明',
              width: 100,
              path: '',
              level: 0,
              lang: {
                headerName: {
                  zh_CN: '存提码说明',
                  zh_TW: '存提码说明',
                  en_US: 'deposit_info_name',
                },
              },
              id: 'ae7dd2ff-e275-4b7b-97f6-b283ef774369',
              columns: [
                {
                  id: '3d327938-d2a4-49e2-aa00-99777033d930',
                  type: 'INPUT',
                  headerName: '存提码',
                  placeholder: '请输入',
                  schema: 'deposit_info',
                  path: 'receipt_checking_no_transfer',
                  disabled: false,
                  editable: true,
                  isFocusDisplay: false,
                  dataType: 'string',
                  tooltipMode: 'normal',
                  iconType: '',
                  lang: {
                    headerName: {
                      zh_CN: '存提码',
                      zh_TW: '存提码',
                      en_US: 'deposit_info',
                    },
                    placeholder: {
                      zh_CN: '请输入',
                      zh_TW: '請輸入',
                      en_US: 'please enter',
                    },
                    extraContent: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                    tooltipTitle: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                  },
                },
                {
                  id: 'a6707924-aa76-4b89-aac5-04446d7bb2eb',
                  type: 'INPUT',
                  headerName: '存提码说明',
                  placeholder: '请输入',
                  schema: 'deposit_info_name',
                  path: 'receipt_checking_no_transfer',
                  disabled: false,
                  editable: true,
                  isFocusDisplay: false,
                  dataType: 'string',
                  tooltipMode: 'normal',
                  iconType: '',
                  lang: {
                    headerName: {
                      zh_CN: '存提码说明',
                      zh_TW: '存提码说明',
                      en_US: 'deposit_info_name',
                    },
                    placeholder: {
                      zh_CN: '请输入',
                      zh_TW: '請輸入',
                      en_US: 'please enter',
                    },
                    extraContent: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                    tooltipTitle: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                  },
                },
              ],
            },
            {
              headerName: '现金变动码说明',
              width: 100,
              path: '',
              level: 0,
              lang: {
                headerName: {
                  zh_CN: '现金变动码说明',
                  zh_TW: '现金变动码说明',
                  en_US: 'cash_change_name',
                },
              },
              id: '2cb388b4-f80c-4440-a475-317d4c2508cc',
              columns: [
                {
                  id: '6fdaa933-469f-40b4-b296-02028f8487a5',
                  type: 'INPUT',
                  headerName: '现金变动码',
                  placeholder: '请输入',
                  schema: 'cash_change_code',
                  path: 'receipt_checking_no_transfer',
                  disabled: false,
                  editable: true,
                  isFocusDisplay: false,
                  dataType: 'string',
                  tooltipMode: 'normal',
                  iconType: '',
                  lang: {
                    headerName: {
                      zh_CN: '现金变动码',
                      zh_TW: '现金变动码',
                      en_US: 'cash_change_code',
                    },
                    placeholder: {
                      zh_CN: '请输入',
                      zh_TW: '請輸入',
                      en_US: 'please enter',
                    },
                    extraContent: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                    tooltipTitle: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                  },
                },
                {
                  id: '756c5fe3-974b-44b4-9efb-686c2c3a7532',
                  type: 'INPUT',
                  headerName: '现金变动码说明',
                  placeholder: '请输入',
                  schema: 'cash_change_name',
                  path: 'receipt_checking_no_transfer',
                  disabled: false,
                  editable: true,
                  isFocusDisplay: false,
                  dataType: 'string',
                  tooltipMode: 'normal',
                  iconType: '',
                  lang: {
                    headerName: {
                      zh_CN: '现金变动码说明',
                      zh_TW: '现金变动码说明',
                      en_US: 'cash_change_name',
                    },
                    placeholder: {
                      zh_CN: '请输入',
                      zh_TW: '請輸入',
                      en_US: 'please enter',
                    },
                    extraContent: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                    tooltipTitle: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                  },
                },
              ],
            },
            {
              headerName: 'url',
              width: 100,
              path: '',
              level: 0,
              lang: {
                headerName: {
                  zh_CN: 'url',
                  zh_TW: 'url',
                  en_US: 'url',
                },
              },
              id: '931b4d5f-5fa2-4c47-b19e-4feaadb3c676',
              columns: [
                {
                  id: '31260789-f80c-4b65-911c-fcaf2bd20d30',
                  type: 'FILE_UPLOAD_DECOUPLE',
                  headerName: 'url',
                  placeholder: '',
                  schema: 'url',
                  path: 'receipt_checking_no_transfer',
                  dataType: 'object',
                  lang: {
                    headerName: {
                      zh_CN: 'url',
                      zh_TW: 'url',
                      en_US: 'url',
                    },
                  },
                  apiMode: 'dataConnector',
                  buckets: '',
                  attribute: {
                    uploadEnable: false,
                    uploadCategory: '',
                    fileExtensions: [],
                    fileCount: '',
                    fileMaxSize: '',
                    draggable: false,
                    disableAam: true,
                    enableEffectAfterSubmit: false,
                    onlyDeleteByOwner: false,
                    filterKey: '',
                  },
                },
              ],
            },
            {
              headerName: '操作列',
              width: 160,
              level: 0,
              lang: {
                headerName: {
                  zh_CN: '操作列',
                  en_US: '',
                  zh_TW: '',
                },
              },
              id: '6a66dd3e-0ec2-4510-9ccc-088d699c60bb',
              pinned: 'right',
              columns: [
                {
                  id: '4d0cf9a6-8e54-4388-a4ec-f9677722222c',
                  type: 'BUTTON_EDIT_ITEM_DECOUPLE',
                  title: '维护',
                  lang: {
                    title: {
                      zh_CN: '维护',
                      zh_TW: '维护',
                      en_US: 'Detail',
                    },
                  },
                  styleMode: 'text',
                  size: 'small',
                  disabled: false,
                  ghost: false,
                  danger: false,
                  block: false,
                  debounce: false,
                  showLoading: false,
                  async: false,
                  debounceTime: 300,
                  targetSchema: 'receipt_checking_no_transfer',
                  targetPath: '',
                  emitConfig: {
                    operationType: 'open_page_edit',
                    dslCode: 'DSL_b08c214210000bb2',
                  },
                  attachMode: 'row',
                },
              ],
            },
          ],
        },
      ],
      hooks: [],
      rules: [],
      variables: [],
      dataConnectors: [],
      globalSetting: {},
    },
  },
  {
    type: 'edit',
    dsl: {
      layout: [],
      hooks: [],
      rules: [],
      variables: [],
      dataConnectors: [],
      globalSetting: {},
    },
  },
];
