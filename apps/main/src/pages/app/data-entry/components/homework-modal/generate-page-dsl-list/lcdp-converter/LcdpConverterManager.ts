import { AthComponentType, Converter, DslData, DslSchema } from './type';
import { commonAthConverter } from './components/common';
import { athenaTableAthConverter } from './components/athena-table';
import { tableGroupAthConverter } from './components/table-group';
import { LcdpFormListConverter } from './components/lcdp-form-list';
import { LcdpInputConverter } from './components/lcdp-input';
import { lcdpDynamicOperationConverter } from './components/lcdp-dynamic-operation';

// 注意：由于当前没有调度中心，只能暂时移植简易版本的转换器
// 之后应当使用 调度中心 里的 完整的 转换器
class LcdpConverterManager {
  private converterMap = new Map<string, Converter>();
  constructor(converterList: Converter[] = []) {
    this.converterMap = new Map(converterList.map((item) => [item.key, item]));
  }

  // 在执行转换器具体转换逻辑之前的前置操作（预留的口子，一般用不到）
  private beforeToDsl(schema: DslSchema): DslSchema {
    return { ...schema };
  }

  // 在执行转换器具体转换逻辑之前的前置操作（预留的口子，一般用不到）
  private beforeToSchema(dsl: DslData): DslData {
    return { ...dsl };
  }

  setConverter(key: string, athConverter: Converter): void {
    this.converterMap.set(key, athConverter);
  }
  deleteConverter(key: string): void {
    this.converterMap.delete(key);
  }
  getConverter(key: string = AthComponentType.COMMON): Converter {
    return this.converterMap.get(key) ?? commonAthConverter;
  }

  private toObjectDsl(schema: DslSchema): DslData {
    const schemaData = this.beforeToDsl(schema);
    const converter = this.getConverter(schemaData.componentName);
    const { data: dslData, childrenData = [], customTransfer } = converter.toDsl(schemaData);
    if (customTransfer) {
      const childDsl = this.toDsl(customTransfer.data);
      customTransfer.callback(dslData, childDsl);
    } else {
      childrenData.forEach((childrenItem) => {
        let target = dslData;
        if (childrenItem.keyPath && childrenItem.keyPath.length > 0) {
          target = childrenItem.keyPath.reduce((acc, key) => {
            if (!acc[key]) {
              acc[key] = {};
            }
            return acc[key];
          }, dslData);
        }
        target[childrenItem.key] = this.toDsl(childrenItem.data);
      });
    }
    return dslData;
  }

  // lowcode的标准schema转dsl
  // 特别说明，可以看到，当前 toDsl 与 toSchema 在入参上存在不对称的情况
  // 设计之初，toDsl 与 toSchema 是对称的，但 由于 需要 适配 平台某些特殊组件的dsl 结构，才产生了 差异
  // 但对于 对外的 使用 和 对 具体 转换器的 定制开发，没有太大影响
  toDsl(schema: DslSchema | DslSchema[]): DslData | DslData[] {
    if (Object.prototype.toString.call(schema) === '[object Array]') {
      return schema.map((schemaOrigin: DslSchema) => {
        return this.toObjectDsl(schemaOrigin);
      });
    }
    return this.toObjectDsl(schema as DslSchema);
  }

  // dsl转lowcode的标准schema
  toSchema(dsl: DslData[]): DslSchema[] {
    return dsl.map((dslOrigin: DslData) => {
      const dslData = this.beforeToSchema(dslOrigin);
      const converter = this.getConverter(dslData.type);
      const { data: dslSchema, childrenData = [] } = converter.toSchema(dslData);
      childrenData.forEach((childrenItem) => {
        dslSchema[childrenItem.key] = this.toSchema(childrenItem.data);
      });
      return dslSchema;
    });
  }
}

export const lcdpConverterManager = new LcdpConverterManager([
  commonAthConverter,
  athenaTableAthConverter,
  tableGroupAthConverter,
  LcdpFormListConverter,
  LcdpInputConverter,
  lcdpDynamicOperationConverter,
]);
