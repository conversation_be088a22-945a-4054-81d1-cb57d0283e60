import { cloneDeep, isObject } from 'lodash';
import { GenerateBusinessType } from '../homework-modal.tool';
import { dataConnectorTemplate, pageDslTemplateMap, baseGenerateAthComponentSchemaMap } from './config/config';
import { EspActionField, LangObject, CategoryComponentMap, EspActionMap, GenerateAthComponentType } from './type';
import { lcdpConverterManager } from './lcdp-converter/LcdpConverterManager';
import { v4 as uuidv4 } from 'uuid';
import { DslData } from 'components/page-design/components/dsl-form-render/dsl-form-render.type';
import { last } from 'lodash';

// 关于在这里产生dsl数据我个人是不建议的，在这里并没法取到lowcode组件的最新meta信息以及转换器，且无法复用设计器中现成的拖拽生成逻辑
// 但既然po觉得在进入设计器之前就需要产生dsl，且按照“模版“的概念维护dsl模版，那么还是照做
// 所以在这里，会根据标准场景维护几套静态dsl配置，也就是 以后 会 移动到 数据库里的 dsl 模版
// 依照模版数据组装和调整所需dsl
// 但其实弊端很明显：1.如果dsl结构变动后需要更新模版和处理逻辑 2.无法和设计器复用生成逻辑 3.无法复用转换器（除非调度中心完成）
// 在这里的generate逻辑一部分来自于模版，一部分来自于静态meta（没法直接用设计器最新的meta）
// 所以后续的逻辑更新，不仅涉及到，模版的更新，还涉及到静态meta的更新
// 我更希望的是模版决定结构，具体内容的插入来自于页面类型，action的字段和meta

export const generatePageDslList = (
  formValues: {
    addPageDesignType: GenerateBusinessType;
    listActionId: string;
    formActionId?: string;
    addActionId?: string;
    editActionId?: string;
    deleteActionId?: string;
  },
  espActionMap: EspActionMap,
  lang: string = 'zh_CN',
): any[] => {
  const { addPageDesignType, listActionId, formActionId, addActionId, editActionId, deleteActionId } = formValues;
  const pageDslList = pageDslTemplateMap[addPageDesignType];
  if (!pageDslList) return null;

  // 这里 就根据固定 结构进行操作，部分来自于模版，部分动态生成
  switch (addPageDesignType) {
    case GenerateBusinessType.BASIC_TABLE: {
      const listActionEspAction = espActionMap[listActionId];
      const formActionIdEspAction = espActionMap[formActionId];
      const addActionIdEspAction = espActionMap[addActionId];
      const editActionIdEspAction = espActionMap[editActionId];
      const deleteActionIdEspAction = espActionMap[deleteActionId];

      if (!listActionEspAction || !formActionIdEspAction) return null;

      // 浏览界面
      const browsePageDsl = cloneDeep(pageDslList[0]);
      const browsePageCode = `DSL_${uuidv4()}`;
      // 编辑界面
      const editPageDsl = cloneDeep(pageDslList[1]);
      const editPageCode = `DSL_${uuidv4()}`;

      // 处理浏览界面
      let { layout: browseLayout, dataConnectors: browseDataConnectors } = browsePageDsl.dsl;
      const browseTableDsl = lcdpConverterManager.toDsl(getNodeSchemaData(listActionEspAction.espActionField, lang));
      if ((browseTableDsl as DslData).type === GenerateAthComponentType.ATHENA_TABLE) {
        // 选中的action产生的是表格才会处理模版逻辑，否则不符合规则

        const templateSlots = browseLayout?.[0]?.['slots'];
        const templateOperationColumnDef = last<any>(browseLayout?.[0]?.['columnDefs']);

        // 新增
        templateSlots[0]['group'][0]['group'][0].targetSchema = listActionEspAction.espActionField.data_name;
        templateSlots[0]['group'][0]['group'][0].emitConfig.dslCode = editPageCode;

        // 删除
        templateSlots[0]['group'][0]['group'][1].targetSchema = listActionEspAction.espActionField.data_name;
        templateSlots[0]['group'][0]['group'][1].action.dataConnectorId =
          deleteActionIdEspAction?.espActionField.data_name ?? '';

        // 维护
        templateOperationColumnDef.columns[0].targetSchema = listActionEspAction.espActionField.data_name;
        templateOperationColumnDef.columns[0].emitConfig.dslCode = editPageCode;

        browseTableDsl['slots'] = templateSlots;
        browseTableDsl['columnDefs'].push(templateOperationColumnDef);
      }

      browseLayout = [browseTableDsl];
      browseDataConnectors = [
        getDataConnectorByAction(listActionEspAction.actionData, listActionEspAction.espActionField, lang, true),
      ];
      if (deleteActionIdEspAction) {
        browseDataConnectors.push(
          getDataConnectorByAction(
            deleteActionIdEspAction.actionData,
            deleteActionIdEspAction.espActionField,
            lang,
            true,
          ),
        );
      }

      // 处理浏览界面
      let { layout: editLayout, dataConnectors: editDataConnectors } = editPageDsl.dsl;
      const editFormDsl = lcdpConverterManager.toDsl(getNodeSchemaData(formActionIdEspAction.espActionField, lang));
      const editButtonGroup = editLayout?.[1];
      // 保存并新增
      editButtonGroup.group[1].targetSchema = formActionIdEspAction.espActionField.data_name;
      editButtonGroup.group[1].action.dataConnectorId = editActionIdEspAction?.espActionField.data_name ?? '';
      // 保存
      editButtonGroup.group[2].targetSchema = formActionIdEspAction.espActionField.data_name;
      editButtonGroup.group[2].action.dataConnectorId = editActionIdEspAction?.espActionField.data_name ?? '';

      editLayout = [editFormDsl, editButtonGroup];
      editDataConnectors = [
        getDataConnectorByAction(formActionIdEspAction.actionData, formActionIdEspAction.espActionField, lang, true),
      ];
      if (addActionIdEspAction) {
        editDataConnectors.push(
          getDataConnectorByAction(addActionIdEspAction.actionData, addActionIdEspAction.espActionField, lang, true),
        );
      }
      if (editActionIdEspAction) {
        editDataConnectors.push(
          getDataConnectorByAction(editActionIdEspAction.actionData, editActionIdEspAction.espActionField, lang, true),
        );
      }

      return [
        {
          ...browsePageDsl,
          dsl: {
            ...browsePageDsl.dsl,
            layout: browseLayout,
            dataConnectors: browseDataConnectors,
          },
        },
        {
          ...editPageDsl,
          dsl: {
            ...editPageDsl.dsl,
            layout: editLayout,
            dataConnectors: editDataConnectors,
          },
        },
      ];
    }
    case GenerateBusinessType.EDITABLE_TABLE: {
      const listActionEspAction = espActionMap[listActionId];
      const addActionIdEspAction = espActionMap[addActionId];
      const editActionIdEspAction = espActionMap[editActionId];
      const deleteActionIdEspAction = espActionMap[deleteActionId];

      if (!listActionEspAction) return null;

      const designPageDsl = cloneDeep(pageDslList[0]);
      const code = `DSL_${uuidv4()}`;
      designPageDsl.code = code;
      const { layout } = designPageDsl.dsl;

      // 处理主表
      const mainDsl = lcdpConverterManager.toDsl(getNodeSchemaData(listActionEspAction.espActionField, lang));
      if ((mainDsl as DslData).type === GenerateAthComponentType.ATHENA_TABLE) {
        // 选中的action产生的是表格才会处理模版逻辑，否则不符合规则
        const templateSlots = layout?.[0]?.['slots'];
        const templateOperationColumnDef = last<any>(layout?.[0]?.['columnDefs']);
        // 新增
        templateSlots[0]['group'][0]['group'][0].targetSchema = listActionEspAction.espActionField.data_name;
        // 保存按钮
        templateSlots[0]['group'][0]['group'][1].targetSchema = listActionEspAction.espActionField.data_name;
        // 保存按钮-新建
        templateSlots[0]['group'][0]['group'][1].action.combineActions[0].dataConnectorId =
          addActionIdEspAction?.espActionField.data_name ?? '';
        // 保存按钮-保存
        templateSlots[0]['group'][0]['group'][1].action.combineActions[1].dataConnectorId =
          editActionIdEspAction?.espActionField.data_name ?? '';
        // 操作栏位-删除
        templateOperationColumnDef.columns[0].targetSchema = listActionEspAction.espActionField.data_name;
        templateOperationColumnDef.columns[0].operation.dataConnectorId =
          deleteActionIdEspAction?.espActionField.data_name ?? '';

        mainDsl['slots'] = templateSlots;
        mainDsl['columnDefs'].push(templateOperationColumnDef);
      }

      const dataConnectors = [
        getDataConnectorByAction(listActionEspAction.actionData, listActionEspAction.espActionField, lang, true),
      ];

      if (addActionIdEspAction) {
        dataConnectors.push(
          getDataConnectorByAction(addActionIdEspAction.actionData, addActionIdEspAction.espActionField, lang, true),
        );
      }
      if (editActionIdEspAction) {
        dataConnectors.push(
          getDataConnectorByAction(editActionIdEspAction.actionData, editActionIdEspAction.espActionField, lang, true),
        );
      }
      if (deleteActionIdEspAction) {
        dataConnectors.push(
          getDataConnectorByAction(
            deleteActionIdEspAction.actionData,
            deleteActionIdEspAction.espActionField,
            lang,
            true,
          ),
        );
      }

      return [
        {
          ...designPageDsl,
          dsl: {
            ...designPageDsl.dsl,
            layout: [mainDsl],
            dataConnectors,
          },
        },
      ];
    }
    default: {
      return pageDslList;
    }
  }
};

// 通过action组装dataConnectors
export const getDataConnectorByAction = (
  actionData:{
    actionId: string;
    provider: string;
    serviceName: string;
  },
  espActionFields: EspActionField,
  lang: string = 'zh_CN',
  needId = false,
) => {
  if (!actionData || !espActionFields) return null;
  const dataConnector = cloneDeep(dataConnectorTemplate);
  dataConnector.name = espActionFields.data_name;
  dataConnector.description = espActionFields.description[lang];
  dataConnector['lang']['description'] = espActionFields.description;
  const digi_service = dataConnector?.option?.request?.headers?.find((s) => s.key === 'digi-service');
  if (isObject(digi_service.value)) {
    digi_service.value.prod = actionData.provider;
    digi_service.value.name = actionData.serviceName;
    digi_service.value = JSON.stringify(digi_service.value);
  }
  dataConnector.option.response.meta = espActionFields;
  if (needId) dataConnector.id = uuidv4();
  return dataConnector;
};

// ============ 移植并改造自动生成逻辑 ============
export function removeLastPart(str: string) {
  const lastDotIndex = str.lastIndexOf('.');
  if (lastDotIndex === -1) return '';
  return str.substring(0, lastDotIndex);
}

/
const getPlaceHolder = (type: GenerateAthComponentType, description: LangObject) => {
  if ([GenerateAthComponentType.INPUT].includes(type)) {
    return {
      placeholder: {
        zh_TW: '請輸入' + description['zh_TW'],
        en_US: 'Please Input ' + description['en_US'],
        zh_CN: '请输入' + description['zh_CN'],
      },
    };
  } else if (
    [
      GenerateAthComponentType.SELECT,
      GenerateAthComponentType.DATEPICKER,
      GenerateAthComponentType.TIMEPICKER,
    ].includes(type)
  ) {
    return {
      placeholder: {
        zh_TW: '請選擇' + description['zh_TW'],
        en_US: 'Please Select ' + description['en_US'],
        zh_CN: '请选择' + description['zh_CN'],
      },
    };
  } else {
    return {};
  }
};

const getComponentSchema = (componentName: GenerateAthComponentType, extraDslInfo = {}): any => {
  const componentSnippetDslInfo = baseGenerateAthComponentSchemaMap[componentName]?.props?.dslInfo ?? {};
  return {
    componentName: componentName,
    props: {
      dslInfo: { ...componentSnippetDslInfo, ...extraDslInfo, type: componentName },
    },
  };
};

export const getExtraDslInfo = (node: EspActionField, componentName: GenerateAthComponentType, locale = 'zh_CN') => {
  const { is_array } = node;
  if (is_array) {
    return {
      tableTitle: node.description[locale],
      lang: {
        tableTitle: node.description,
      },
      schema: node.data_name,
      path: removeLastPart(node.fullPath),
    };
  }

  if (componentName === GenerateAthComponentType.TABLE_GROUP) {
    return {
      headerName: node.description[locale],
      lang: {
        headerName: node.description,
      },
      path: removeLastPart(node.fullPath),
    };
  }

  return {
    headerName: node.description[locale],
    lang: {
      headerName: node.description,
      ...getPlaceHolder(componentName, node.description),
    },
    schema: node.data_name,
    path: removeLastPart(node.fullPath),
  };
};

// 获取组件的dslInfo
export const getNodeSchemaData = (
  node: EspActionField,
  locale = 'zh_CN',
  appointComponentName?: GenerateAthComponentType,
): any => {
  const { data_type, is_array } = node;
  const category = is_array ? 'array' : data_type;
  const componentName = appointComponentName ?? GenerateAthComponentType[CategoryComponentMap[category]];
  const children = (node?.field ?? []).filter(
    (child) => (!child.field || child.field.length === 0) && child.data_name !== 'manage_status',
  );

  const extraDslInfo = getExtraDslInfo(node, componentName, locale);
  const returnData = getComponentSchema(componentName, extraDslInfo);

  if (children.length > 0) {
    returnData.children = children.map((childNode: EspActionField) => {
      if (componentName === GenerateAthComponentType.ATHENA_TABLE) {
        // ATHENA_TABLE 需要组装一层 TABLE_GROUP
        const tableGroupNodeData = getNodeSchemaData(
          {
            ...childNode,
            field: [childNode],
          },
          locale,
          GenerateAthComponentType.TABLE_GROUP,
        );
        console.log('tableGroupNodeData:', tableGroupNodeData);
        return tableGroupNodeData;
      }

      return getNodeSchemaData(childNode, locale);
    });
  }

  if (componentName === GenerateAthComponentType.ATHENA_TABLE || componentName === GenerateAthComponentType.FORM_LIST) {
    returnData.children?.unshift(baseGenerateAthComponentSchemaMap[GenerateAthComponentType.DYNAMIC_OPERATION]);
  }

  return returnData;
};
