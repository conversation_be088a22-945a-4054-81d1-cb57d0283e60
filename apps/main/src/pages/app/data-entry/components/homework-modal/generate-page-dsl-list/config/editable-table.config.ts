export const editableTableDslList = [
  {
    type: 'design',
    dsl: {
      layout: [
        {
          schema: '',
          isSort: true,
          saveColumnsWidth: true,
          suppressAutoAddRow: false,
          rowDelete: true,
          adaptiveModel: 'default',
          type: 'ATHENA_TABLE',
          setting: {
            hideDefaultToolbar: [],
            options: [],
          },
          path: '',
          rowSpanTree: false,
          checkbox: true,
          rowIndex: false,
          id: 'b0a19220-c51e-4aa6-a074-b9cf18d803f1',
          lang: {
            tableTitle: {
              zh_CN: '',
              en_US: '',
              zh_TW: '',
            },
          },
          tableTitle: '',
          queryInfo: {
            pageInfo: {
              pageSize: 50,
            },
            dataFilter: '',
            isAsync: true,
            dataSourceType: 'dataConnector',
            dataConnectorId: 'receipt_checking_full',
          },
          rowHeight: 100,
          slots: [
            {
              select: 'slot-top-right',
              height: 140,
              group: [
                {
                  id: 'cb120bc8-2360-4294-bfbf-0a0f2bfb7096',
                  type: 'BUTTON_GROUP',
                  headerName: '按钮组',
                  block: true,
                  verticalAlignment: 'center',
                  justifyContent: 'flex-start',
                  gap: '12px',
                  lang: {
                    headerName: {
                      zh_CN: '按钮组',
                      zh_TW: '按鈕組',
                      en_US: 'Button Group',
                    },
                  },
                  moreButtonConfig: {
                    enable: false,
                  },
                  group: [
                    {
                      id: 'a89d23d8-a470-478d-8816-edcdf3fbb3ef',
                      type: 'BUTTON_ADD_ITEM_DECOUPLE',
                      title: '新增',
                      lang: {
                        title: {
                          zh_CN: '新增',
                          zh_TW: '新增行',
                          en_US: 'Add Row',
                        },
                      },
                      styleMode: 'primary',
                      size: 'small',
                      disabled: false,
                      ghost: false,
                      danger: false,
                      block: false,
                      debounce: false,
                      showLoading: false,
                      async: false,
                      debounceTime: 300,
                      targetSchema: 'receipt_checking',
                      targetPath: '',
                      attachMode: 'all',
                      operation: {
                        dataSourceType: 'dataConnector',
                        dataConnectorId: '',
                        extendedFields: null,
                      },
                    },
                    {
                      id: 'e79facb3-9e5d-4e94-bd06-206f7a384503',
                      type: 'BUTTON_COMBINE_SAVE_DECOUPLE',
                      title: '保存',
                      lang: {
                        title: {
                          zh_CN: '保存',
                          zh_TW: '存檔',
                          en_US: 'Save',
                        },
                      },
                      styleMode: 'default',
                      size: 'small',
                      disabled: false,
                      ghost: false,
                      danger: false,
                      block: false,
                      debounce: false,
                      showLoading: false,
                      async: false,
                      debounceTime: 300,
                      trailingAction: 'reload-page',
                      targetSchema: 'receipt_checking',
                      targetPath: '',
                      action: {
                        type: '',
                        actionType: 'basic-data-combine-save',
                        submitType: {
                          schema: '',
                          isBatch: false,
                        },
                        trackCode: 'SUBMIT',
                        combineActions: [
                          {
                            dataSourceType: 'dataConnector',
                            dataConnectorId: 'receipt_checking_create',
                            title: '新建',
                            lang: {
                              title: {
                                zh_CN: '新建',
                                zh_TW: '新建',
                                en_US: 'Create',
                              },
                            },
                          },
                          {
                            dataSourceType: 'dataConnector',
                            dataConnectorId: 'receipt_checking_update',
                            title: '保存',
                            lang: {
                              title: {
                                zh_CN: '保存',
                                zh_TW: '存檔',
                                en_US: 'Save',
                              },
                            },
                          },
                        ],
                      },
                    },
                  ],
                },
              ],
            },
          ],
          columnDefs: [
            {
              headerName: '抛转状态',
              width: 100,
              path: '',
              level: 0,
              lang: {
                headerName: {
                  zh_CN: '抛转状态',
                  zh_TW: '',
                  en_US: '',
                },
              },
              id: 'f44f1dd4-2e4f-49a6-acfc-c3867c5628e9',
              columns: [
                {
                  id: 'dfe48db7-2f66-43cb-a417-5e602f8212e9',
                  type: 'SELECT',
                  headerName: '抛转状态',
                  placeholder: '请选择',
                  schema: 'generate_status',
                  disabled: true,
                  editable: false,
                  isFocusDisplay: false,
                  dataType: 'string',
                  tooltipMode: 'normal',
                  iconType: '',
                  options: [
                    {
                      lang: {
                        title: {
                          en_US: '已抛转',
                          zh_CN: '已抛转',
                          zh_TW: '已抛转',
                        },
                      },
                      value: '1',
                      title: '已抛转',
                    },
                    {
                      lang: {
                        title: {
                          en_US: '未抛转',
                          zh_CN: '未抛转',
                          zh_TW: '未抛转',
                        },
                      },
                      value: '2',
                      title: '未抛转',
                    },
                  ],
                  lang: {
                    headerName: {
                      zh_CN: '抛转状态',
                      zh_TW: '',
                      en_US: '',
                    },
                    placeholder: {
                      zh_CN: '请选择',
                      zh_TW: '請選擇',
                      en_US: 'please select',
                    },
                    extraContent: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                    tooltipTitle: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                  },
                  path: 'receipt_checking',
                  dataConnectorId: '',
                  optionsType: 'dataConnector',
                },
              ],
            },
            {
              headerName: '数据源',
              width: 100,
              path: '',
              level: 0,
              lang: {
                headerName: {
                  zh_CN: '数据源',
                  zh_TW: '',
                  en_US: '',
                },
              },
              id: '4346394e-b602-4e2b-9757-4e1dff24b489',
              columns: [
                {
                  id: 'f2d541a7-59ca-4c26-9b33-ee3ca610e5fb',
                  type: 'SELECT',
                  headerName: '数据源',
                  placeholder: '请选择',
                  schema: 'data_source',
                  path: 'receipt_checking',
                  disabled: false,
                  editable: true,
                  isFocusDisplay: false,
                  dataType: 'string',
                  tooltipMode: 'normal',
                  iconType: '',
                  options: [
                    {
                      lang: {
                        title: {
                          en_US: '手动导入',
                          zh_CN: '手动导入',
                          zh_TW: '手动导入',
                        },
                      },
                      value: '0',
                      title: '手动导入',
                    },
                    {
                      lang: {
                        title: {
                          en_US: '银企直联导入',
                          zh_CN: '银企直联导入',
                          zh_TW: '银企直联导入',
                        },
                      },
                      value: '1',
                      title: '银企直联导入',
                    },
                  ],
                  lang: {
                    headerName: {
                      zh_CN: '数据源',
                      zh_TW: '',
                      en_US: '',
                    },
                    placeholder: {
                      zh_CN: '请选择',
                      zh_TW: '請選擇',
                      en_US: 'please select',
                    },
                    extraContent: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                    tooltipTitle: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                  },
                  dataConnectorId: '',
                  optionsType: 'dataConnector',
                },
              ],
            },
            {
              headerName: '资金中心',
              width: 100,
              path: '',
              level: 0,
              lang: {
                headerName: {
                  zh_CN: '资金中心',
                  zh_TW: '',
                  en_US: '',
                },
              },
              id: 'a0545a51-abd9-4bbd-bcfd-e777c733416d',
              columns: [
                {
                  id: '49a5ee27-5b44-4aa7-875a-303644ca1915',
                  type: 'OPERATION_EDITOR',
                  headerName: '资金中心',
                  placeholder: '请输入',
                  schema: 'cashing_center',
                  path: 'receipt_checking_full',
                  disabled: false,
                  editable: true,
                  isFocusDisplay: false,
                  showInput: false,
                  dataType: 'string',
                  lang: {
                    headerName: {
                      zh_CN: '资金中心',
                      zh_TW: '',
                      en_US: '',
                    },
                    placeholder: {
                      zh_CN: '请输入',
                      zh_TW: '請輸入',
                      en_US: 'please enter',
                    },
                    extraContent: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                    tooltipTitle: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                  },
                  operations: [
                    {
                      attach: {
                        mode: 'row',
                      },
                      sequence: 0,
                      operate: 'openwindow',
                      name: '开窗',
                      id: '2ab32d9d-6e21-4aeb-94c1-a270e2690601',
                      openWindowDefine: {
                        title: '公司资料',
                        lang: {
                          title: {
                            en_US: '公司资料',
                            zh_CN: '公司资料',
                            zh_TW: '公司资料',
                          },
                        },
                        allAction: {
                          searchInfos: [],
                        },
                        multipleSelect: false,
                        applyToArray: false,
                        supportBatch: false,
                        selectedFirstRow: false,
                        useHasNext: true,
                        enableInputSearch: true,
                        buttons: [
                          {
                            id: 'confirm',
                            title: '提交',
                            lang: {
                              title: {
                                zh_TW: '提交',
                                en_US: 'Submit',
                                zh_CN: '提交',
                              },
                            },
                            language: {
                              title: {
                                zh_TW: '提交',
                                en_US: 'Submit',
                                zh_CN: '提交',
                              },
                            },
                            actions: [
                              {
                                backFills: [
                                  {
                                    key: 'cashing_center',
                                    valueScript: "selectedObject['om_company_no']",
                                  },
                                  {
                                    key: 'cashing_center_name',
                                    valueScript: "selectedObject['om_company_name']",
                                  },
                                  {
                                    key: 'legal',
                                    valueScript: "selectedObject['belong_legal_person_no']",
                                  },
                                  {
                                    key: 'legal_name',
                                    valueScript: "selectedObject['belong_legal_person_name']",
                                  },
                                ],
                                type: 'UI',
                              },
                            ],
                          },
                        ],
                        type: 'dataConnector',
                        dataConnectorId: 'om_company_info',
                        layout: [
                          {
                            type: 'ATHENA_TABLE',
                            path: '',
                            dataType: 'array',
                            checkbox: false,
                            rowDelete: false,
                            rowIndex: false,
                            rowSpanTree: false,
                            schema: '',
                            id: '66CB702DE63A4AF6894C8518C4FFB42D',
                            columnDefs: [
                              {
                                headerName: '公司编号',
                                path: 'om_company_info',
                                level: 0,
                                columns: [
                                  {
                                    type: 'INPUT',
                                    schema: 'om_company_no',
                                    path: 'om_company_info',
                                    dataType: 'string',
                                    editable: false,
                                    disabled: true,
                                    headerName: '公司编号',
                                    label: '公司编号',
                                    placeholder: '公司编号',
                                    lang: {
                                      headerName: {
                                        en_US: '',
                                        zh_CN: '公司编号',
                                        zh_TW: '',
                                        label: '公司编号',
                                      },
                                      placeholder: {
                                        label: '公司编号',
                                        zh_CN: '公司编号',
                                        zh_TW: '',
                                        en_US: '',
                                      },
                                      label: {
                                        en_US: '',
                                        zh_CN: '公司编号',
                                        zh_TW: '',
                                        label: '公司编号',
                                      },
                                    },
                                    isFocusDisplay: false,
                                    id: '87067405B36A4E1BAAC49857D342AB34',
                                    sortable: true,
                                    filterable: true,
                                  },
                                ],
                                width: 160,
                                lang: {
                                  headerName: {
                                    en_US: '',
                                    zh_CN: '公司编号',
                                    zh_TW: '',
                                    label: '公司编号',
                                  },
                                },
                                id: '9BC35B61F1A047588CD348D1A3B925F8',
                              },
                              {
                                headerName: '公司名称',
                                path: 'om_company_info',
                                level: 0,
                                columns: [
                                  {
                                    type: 'INPUT',
                                    schema: 'om_company_name',
                                    path: 'om_company_info',
                                    dataType: 'string',
                                    editable: false,
                                    disabled: true,
                                    headerName: '公司名称',
                                    label: '公司名称',
                                    placeholder: '公司名称',
                                    lang: {
                                      headerName: {
                                        en_US: '',
                                        zh_CN: '公司名称',
                                        zh_TW: '',
                                        label: '公司名称',
                                      },
                                      placeholder: {
                                        label: '公司名称',
                                        zh_CN: '公司名称',
                                        zh_TW: '',
                                        en_US: '',
                                      },
                                      label: {
                                        en_US: '',
                                        zh_CN: '公司名称',
                                        zh_TW: '',
                                        label: '公司名称',
                                      },
                                    },
                                    isFocusDisplay: false,
                                    id: 'E6A2801053754365A7E6B6E106D94BA1',
                                    sortable: true,
                                    filterable: true,
                                  },
                                ],
                                width: 160,
                                lang: {
                                  headerName: {
                                    en_US: '',
                                    zh_CN: '公司名称',
                                    zh_TW: '',
                                    label: '公司名称',
                                  },
                                },
                                id: '4EBAA07E0325463AA6DBE157DCB02CAC',
                              },
                              {
                                headerName: '对内全称',
                                path: 'om_company_info',
                                level: 0,
                                columns: [
                                  {
                                    type: 'INPUT',
                                    schema: 'internal_full_name',
                                    path: 'om_company_info',
                                    dataType: 'string',
                                    editable: false,
                                    disabled: true,
                                    headerName: '对内全称',
                                    label: '对内全称',
                                    placeholder: '对内全称',
                                    lang: {
                                      headerName: {
                                        en_US: '',
                                        zh_CN: '对内全称',
                                        zh_TW: '',
                                        label: '对内全称',
                                      },
                                      placeholder: {
                                        label: '对内全称',
                                        zh_CN: '对内全称',
                                        zh_TW: '',
                                        en_US: '',
                                      },
                                      label: {
                                        en_US: '',
                                        zh_CN: '对内全称',
                                        zh_TW: '',
                                        label: '对内全称',
                                      },
                                    },
                                    isFocusDisplay: false,
                                    id: '386771E773324B4C8B1F740CFBBB7E2C',
                                    sortable: true,
                                    filterable: true,
                                  },
                                ],
                                width: 160,
                                lang: {
                                  headerName: {
                                    en_US: '',
                                    zh_CN: '对内全称',
                                    zh_TW: '',
                                    label: '对内全称',
                                  },
                                },
                                id: '2C5BDA3A5CD948ECA29655DD5BF11ED0',
                              },
                              {
                                headerName: '对外全称',
                                path: 'om_company_info',
                                level: 0,
                                columns: [
                                  {
                                    type: 'INPUT',
                                    schema: 'external_full_name',
                                    path: 'om_company_info',
                                    dataType: 'string',
                                    editable: false,
                                    disabled: true,
                                    headerName: '对外全称',
                                    label: '对外全称',
                                    placeholder: '对外全称',
                                    lang: {
                                      headerName: {
                                        en_US: '',
                                        zh_CN: '对外全称',
                                        zh_TW: '',
                                        label: '对外全称',
                                      },
                                      placeholder: {
                                        label: '对外全称',
                                        zh_CN: '对外全称',
                                        zh_TW: '',
                                        en_US: '',
                                      },
                                      label: {
                                        en_US: '',
                                        zh_CN: '对外全称',
                                        zh_TW: '',
                                        label: '对外全称',
                                      },
                                    },
                                    isFocusDisplay: false,
                                    id: '2605E07571C84F988F1013ABAB92459C',
                                    sortable: true,
                                    filterable: true,
                                  },
                                ],
                                width: 160,
                                lang: {
                                  headerName: {
                                    en_US: '',
                                    zh_CN: '对外全称',
                                    zh_TW: '',
                                    label: '对外全称',
                                  },
                                },
                                id: '1519EE330DA04B9DB30D90240877A7AE',
                              },
                            ],
                            allFields: [],
                            setting: {
                              orderFields: [],
                              hideDefaultToolbar: [],
                              options: [],
                            },
                            hideDefaultToolbar: [],
                            saveColumnsWidth: true,
                            suppressAutoAddRow: false,
                            disabledUserDefined: true,
                            tableTitle: '',
                            lang: {
                              tableTitle: {
                                zh_CN: '',
                                zh_TW: '',
                                en_US: '',
                              },
                            },
                            checkboxOperation: false,
                            openRowHeight: true,
                          },
                        ],
                      },
                      description: '',
                      lang: {
                        description: {
                          zh_CN: '',
                          zh_TW: '',
                          en_US: '',
                        },
                      },
                      navigationConfig: {
                        enable: false,
                        url: 'base-data-entry',
                        urlParams: {},
                      },
                    },
                  ],
                  fields: [
                    {
                      schema: 'cashing_center',
                      show: false,
                    },
                    {
                      schema: 'cashing_center_name',
                      show: true,
                    },
                    {
                      schema: 'legal',
                      show: false,
                    },
                    {
                      schema: 'legal_name',
                      show: false,
                    },
                  ],
                },
                {
                  id: '06726d76-3cf0-4440-b2f5-4607657062ec',
                  type: 'OPERATION_EDITOR',
                  headerName: '银行交易账户',
                  placeholder: '请输入',
                  schema: 'bank_trans_code',
                  path: 'receipt_checking',
                  disabled: false,
                  editable: true,
                  isFocusDisplay: false,
                  showInput: false,
                  dataType: 'string',
                  lang: {
                    headerName: {
                      en_US: '银行交易账户',
                      zh_CN: '银行交易账户',
                      zh_TW: '银行交易账户',
                    },
                    placeholder: {
                      zh_CN: '请输入',
                      zh_TW: '請輸入',
                      en_US: 'please enter',
                    },
                    extraContent: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                    tooltipTitle: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                  },
                  operations: [
                    {
                      attach: {
                        mode: 'row',
                      },
                      sequence: 0,
                      operate: 'openwindow',
                      name: '开窗',
                      id: 'b8a93b61-0cd4-469b-9d4f-5789392ec8de',
                      openWindowDefine: {
                        title: '银行账户资料',
                        lang: {
                          title: {
                            en_US: '银行账户资料',
                            zh_CN: '银行账户资料',
                            zh_TW: '银行账户资料',
                          },
                        },
                        allAction: {
                          searchInfos: [],
                        },
                        multipleSelect: false,
                        applyToArray: false,
                        supportBatch: false,
                        selectedFirstRow: false,
                        useHasNext: true,
                        enableInputSearch: true,
                        buttons: [
                          {
                            id: 'confirm',
                            title: '提交',
                            lang: {
                              title: {
                                zh_TW: '提交',
                                en_US: 'Submit',
                                zh_CN: '提交',
                              },
                            },
                            language: {
                              title: {
                                zh_TW: '提交',
                                en_US: 'Submit',
                                zh_CN: '提交',
                              },
                            },
                            actions: [
                              {
                                backFills: [
                                  {
                                    key: 'bank_trans_code',
                                    valueScript: "selectedObject['transaction_account_no']",
                                  },
                                  {
                                    key: 'bank_trans_name',
                                    valueScript: "selectedObject['account_name']",
                                  },
                                ],
                                type: 'UI',
                              },
                            ],
                          },
                        ],
                        type: 'dataConnector',
                        dataConnectorId: 'company_bank_account_data',
                        layout: [
                          {
                            type: 'ATHENA_TABLE',
                            path: '',
                            dataType: 'array',
                            checkbox: false,
                            rowDelete: false,
                            rowIndex: false,
                            rowSpanTree: false,
                            schema: '',
                            id: '3AEC04C8CEA64026926CB36C3B0A662D',
                            columnDefs: [
                              {
                                headerName: '交易账户编码',
                                path: 'company_bank_account_data',
                                level: 0,
                                columns: [
                                  {
                                    type: 'INPUT',
                                    schema: 'transaction_account_no',
                                    path: 'company_bank_account_data',
                                    dataType: 'string',
                                    editable: false,
                                    disabled: true,
                                    headerName: '交易账户编码',
                                    label: '交易账户编码',
                                    placeholder: '交易账户编码',
                                    lang: {
                                      headerName: {
                                        en_US: '',
                                        zh_CN: '交易账户编码',
                                        zh_TW: '',
                                        label: '交易账户编码',
                                      },
                                      placeholder: {
                                        label: '交易账户编码',
                                        zh_CN: '交易账户编码',
                                        zh_TW: '',
                                        en_US: '',
                                      },
                                      label: {
                                        en_US: '',
                                        zh_CN: '交易账户编码',
                                        zh_TW: '',
                                        label: '交易账户编码',
                                      },
                                    },
                                    isFocusDisplay: false,
                                    id: 'F69EEBE56DC24921BC9E56EC9BD97E00',
                                    sortable: true,
                                    filterable: true,
                                  },
                                ],
                                width: 160,
                                lang: {
                                  headerName: {
                                    en_US: '',
                                    zh_CN: '交易账户编码',
                                    zh_TW: '',
                                    label: '交易账户编码',
                                  },
                                },
                                id: 'C268C2CB22304F80950056089B7D4290',
                              },
                              {
                                headerName: '账户名称',
                                path: 'company_bank_account_data',
                                level: 0,
                                columns: [
                                  {
                                    type: 'INPUT',
                                    schema: 'account_name',
                                    path: 'company_bank_account_data',
                                    dataType: 'string',
                                    editable: false,
                                    disabled: true,
                                    headerName: '账户名称',
                                    label: '账户名称',
                                    placeholder: '账户名称',
                                    lang: {
                                      headerName: {
                                        en_US: '',
                                        zh_CN: '账户名称',
                                        zh_TW: '',
                                        label: '账户名称',
                                      },
                                      placeholder: {
                                        label: '账户名称',
                                        zh_CN: '账户名称',
                                        zh_TW: '',
                                        en_US: '',
                                      },
                                      label: {
                                        en_US: '',
                                        zh_CN: '账户名称',
                                        zh_TW: '',
                                        label: '账户名称',
                                      },
                                    },
                                    isFocusDisplay: false,
                                    id: '03144B74224041B894C1CD2F2572387C',
                                    sortable: true,
                                    filterable: true,
                                  },
                                ],
                                width: 160,
                                lang: {
                                  headerName: {
                                    en_US: '',
                                    zh_CN: '账户名称',
                                    zh_TW: '',
                                    label: '账户名称',
                                  },
                                },
                                id: '5AD4E837C23B454F935FE50BC9525580',
                              },
                              {
                                headerName: '交易币种',
                                path: 'company_bank_account_data',
                                level: 0,
                                columns: [
                                  {
                                    type: 'INPUT',
                                    schema: 'transaction_currency_no',
                                    path: 'company_bank_account_data',
                                    dataType: 'string',
                                    editable: false,
                                    disabled: true,
                                    headerName: '交易币种',
                                    label: '交易币种',
                                    placeholder: '交易币种',
                                    lang: {
                                      headerName: {
                                        en_US: '',
                                        zh_CN: '交易币种',
                                        zh_TW: '',
                                        label: '交易币种',
                                      },
                                      placeholder: {
                                        label: '交易币种',
                                        zh_CN: '交易币种',
                                        zh_TW: '',
                                        en_US: '',
                                      },
                                      label: {
                                        en_US: '',
                                        zh_CN: '交易币种',
                                        zh_TW: '',
                                        label: '交易币种',
                                      },
                                    },
                                    isFocusDisplay: false,
                                    id: 'BF1EA952A6BA4718A162550B5343778A',
                                    sortable: true,
                                    filterable: true,
                                  },
                                ],
                                width: 160,
                                lang: {
                                  headerName: {
                                    en_US: '',
                                    zh_CN: '交易币种',
                                    zh_TW: '',
                                    label: '交易币种',
                                  },
                                },
                                id: '5344C3D5B01E496C87DD97C3BA82083E',
                              },
                              {
                                headerName: '开户银行名称',
                                path: 'company_bank_account_data',
                                level: 0,
                                columns: [
                                  {
                                    type: 'INPUT',
                                    schema: 'branch_name',
                                    path: 'company_bank_account_data',
                                    dataType: 'string',
                                    editable: false,
                                    disabled: true,
                                    headerName: '开户银行名称',
                                    label: '开户银行名称',
                                    placeholder: '开户银行名称',
                                    lang: {
                                      headerName: {
                                        en_US: '',
                                        zh_CN: '开户银行名称',
                                        zh_TW: '',
                                        label: '开户银行名称',
                                      },
                                      placeholder: {
                                        label: '开户银行名称',
                                        zh_CN: '开户银行名称',
                                        zh_TW: '',
                                        en_US: '',
                                      },
                                      label: {
                                        en_US: '',
                                        zh_CN: '开户银行名称',
                                        zh_TW: '',
                                        label: '开户银行名称',
                                      },
                                    },
                                    isFocusDisplay: false,
                                    id: '786CCED3BB264FDAACF0B1B4097C2211',
                                    sortable: true,
                                    filterable: true,
                                  },
                                ],
                                width: 160,
                                lang: {
                                  headerName: {
                                    en_US: '',
                                    zh_CN: '开户银行名称',
                                    zh_TW: '',
                                    label: '开户银行名称',
                                  },
                                },
                                id: '89AB3A781778462FB5F620BE774A76B9',
                              },
                              {
                                headerName: '银行账号',
                                path: 'company_bank_account_data',
                                level: 0,
                                columns: [
                                  {
                                    type: 'INPUT',
                                    schema: 'bank_account_no',
                                    path: 'company_bank_account_data',
                                    dataType: 'string',
                                    editable: false,
                                    disabled: true,
                                    headerName: '银行账号',
                                    label: '银行账号',
                                    placeholder: '银行账号',
                                    lang: {
                                      headerName: {
                                        en_US: '',
                                        zh_CN: '银行账号',
                                        zh_TW: '',
                                        label: '银行账号',
                                      },
                                      placeholder: {
                                        label: '银行账号',
                                        zh_CN: '银行账号',
                                        zh_TW: '',
                                        en_US: '',
                                      },
                                      label: {
                                        en_US: '',
                                        zh_CN: '银行账号',
                                        zh_TW: '',
                                        label: '银行账号',
                                      },
                                    },
                                    isFocusDisplay: false,
                                    id: 'E8C67666982747FEA598260D7DE0F8FF',
                                    sortable: true,
                                    filterable: true,
                                  },
                                ],
                                width: 160,
                                lang: {
                                  headerName: {
                                    en_US: '',
                                    zh_CN: '银行账号',
                                    zh_TW: '',
                                    label: '银行账号',
                                  },
                                },
                                id: '1B72B9D6B931461EAC15AA6576DC60B5',
                              },
                            ],
                            allFields: [],
                            setting: {
                              orderFields: [],
                              hideDefaultToolbar: [],
                              options: [],
                            },
                            hideDefaultToolbar: [],
                            saveColumnsWidth: true,
                            suppressAutoAddRow: false,
                            disabledUserDefined: true,
                            tableTitle: '',
                            lang: {
                              tableTitle: {
                                zh_CN: '',
                                zh_TW: '',
                                en_US: '',
                              },
                            },
                            checkboxOperation: false,
                            openRowHeight: true,
                          },
                        ],
                      },
                      description: '',
                      lang: {
                        description: {
                          zh_CN: '',
                          zh_TW: '',
                          en_US: '',
                        },
                      },
                      navigationConfig: {
                        enable: false,
                        url: 'base-data-entry',
                        urlParams: {},
                      },
                    },
                  ],
                  fields: [
                    {
                      schema: 'bank_trans_code',
                      show: true,
                    },
                    {
                      schema: 'bank_trans_name',
                      show: true,
                    },
                  ],
                },
              ],
            },
            {
              headerName: '银行对账单信息',
              width: 160,
              path: '',
              level: 0,
              lang: {
                headerName: {
                  en_US: '银行对账单信息',
                  zh_CN: '银行对账单信息',
                  zh_TW: '银行对账单信息',
                },
              },
              id: '3ef96331-32c4-4e66-ac98-b67781881e27',
              columns: [
                {
                  id: '0cc44ec2-98fe-4f1f-84e9-fa8e8abe18d9',
                  type: 'DATEPICKER',
                  headerName: '交易日期',
                  placeholder: 'yyyyMMdd ',
                  schema: 'trade_date',
                  path: 'receipt_checking',
                  disabled: false,
                  editable: true,
                  isFocusDisplay: false,
                  fieldType: 'datetime',
                  dataType: 'date',
                  tooltipMode: 'normal',
                  iconType: 'wenhao',
                  weekStartsOn: 1,
                  mode: 'date',
                  format: 'yyyy/MM/dd',
                  formatConfig: {
                    type: 'yyyy/MM/dd',
                    value: 'yyyy/MM/dd',
                  },
                  showPickerOptions: false,
                  pickerOptions: [],
                  disabledDate: {
                    type: 'none',
                  },
                  rules: {
                    value: [],
                  },
                  lang: {
                    headerName: {
                      en_US: '交易日期',
                      zh_CN: '交易日期',
                      zh_TW: '交易日期',
                    },
                    placeholder: {
                      zh_CN: 'yyyyMMdd',
                      zh_TW: 'yyyyMMdd',
                      en_US: 'yyyyMMdd',
                    },
                    extraContent: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                    tooltipTitle: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                  },
                },
                {
                  id: '365c390e-4673-43a4-a944-8469d2ac6718',
                  type: 'OPERATION_EDITOR',
                  headerName: '单选开窗',
                  placeholder: '请输入',
                  schema: 'opponent_account_name',
                  path: 'receipt_checking',
                  disabled: false,
                  editable: true,
                  isFocusDisplay: false,
                  showInput: false,
                  dataType: 'string',
                  lang: {
                    headerName: {
                      zh_CN: '单选开窗',
                      zh_TW: '單選開窗',
                      en_US: 'OPERATION EDITOR',
                    },
                    placeholder: {
                      zh_CN: '请输入',
                      zh_TW: '請輸入',
                      en_US: 'please enter',
                    },
                    extraContent: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                    tooltipTitle: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                  },
                  operations: [
                    {
                      attach: {
                        mode: 'row',
                      },
                      sequence: 0,
                      operate: 'openwindow',
                      name: '开窗',
                      id: '7e3c4e74-ed52-4dd8-bacc-5f39cbeaa042',
                      openWindowDefine: {
                        title: '客户银行资料',
                        lang: {
                          title: {
                            en_US: '客户银行资料',
                            zh_CN: '客户银行资料',
                            zh_TW: '客户银行资料',
                          },
                        },
                        allAction: {
                          searchInfos: [],
                        },
                        multipleSelect: false,
                        applyToArray: false,
                        supportBatch: false,
                        selectedFirstRow: false,
                        useHasNext: true,
                        enableInputSearch: true,
                        buttons: [
                          {
                            id: 'confirm',
                            title: '提交',
                            lang: {
                              title: {
                                zh_TW: '提交',
                                en_US: 'Submit',
                                zh_CN: '提交',
                              },
                            },
                            language: {
                              title: {
                                zh_TW: '提交',
                                en_US: 'Submit',
                                zh_CN: '提交',
                              },
                            },
                            actions: [
                              {
                                backFills: [
                                  {
                                    key: 'opponent_account',
                                    valueScript: "selectedObject['bank_account']",
                                  },
                                  {
                                    key: 'opponent_account_name',
                                    valueScript: "selectedObject['bank_account_name']",
                                  },
                                  {
                                    key: 'opponent_branch_name',
                                    valueScript: "selectedObject['branch_name']",
                                  },
                                ],
                                type: 'UI',
                              },
                            ],
                          },
                        ],
                        type: 'dataConnector',
                        dataConnectorId: 'customer_detail_bank',
                        layout: [
                          {
                            type: 'ATHENA_TABLE',
                            path: '',
                            dataType: 'array',
                            checkbox: false,
                            rowDelete: false,
                            rowIndex: false,
                            rowSpanTree: false,
                            schema: '',
                            id: '174EF6796A4B42E3A0CE2339C389744E',
                            columnDefs: [
                              {
                                headerName: '客户编号',
                                path: 'customer_detail_bank',
                                level: 0,
                                columns: [
                                  {
                                    type: 'INPUT',
                                    schema: 'customer_no',
                                    path: 'customer_detail_bank',
                                    dataType: 'string',
                                    editable: false,
                                    disabled: true,
                                    headerName: '客户编号',
                                    label: '客户编号',
                                    placeholder: '客户编号',
                                    lang: {
                                      headerName: {
                                        en_US: '',
                                        zh_CN: '客户编号',
                                        zh_TW: '',
                                        label: '客户编号',
                                      },
                                      placeholder: {
                                        label: '客户编号',
                                        zh_CN: '客户编号',
                                        zh_TW: '',
                                        en_US: '',
                                      },
                                      label: {
                                        en_US: '',
                                        zh_CN: '客户编号',
                                        zh_TW: '',
                                        label: '客户编号',
                                      },
                                    },
                                    isFocusDisplay: false,
                                    id: 'C07E4D478BED4B56A7D6819B3FA13EC4',
                                    sortable: true,
                                    filterable: true,
                                  },
                                ],
                                width: 160,
                                lang: {
                                  headerName: {
                                    en_US: '',
                                    zh_CN: '客户编号',
                                    zh_TW: '',
                                    label: '客户编号',
                                  },
                                },
                                id: 'A9ECB445ACCD44E9A1363B6763FD686C',
                              },
                              {
                                headerName: '客户全名',
                                path: 'customer_detail_bank',
                                level: 0,
                                columns: [
                                  {
                                    type: 'INPUT',
                                    schema: 'customer_full_name',
                                    path: 'customer_detail_bank',
                                    dataType: 'string',
                                    editable: false,
                                    disabled: true,
                                    headerName: '客户全名',
                                    label: '客户全名',
                                    placeholder: '客户全名',
                                    lang: {
                                      headerName: {
                                        en_US: '',
                                        zh_CN: '客户全名',
                                        zh_TW: '',
                                        label: '客户全名',
                                      },
                                      placeholder: {
                                        label: '客户全名',
                                        zh_CN: '客户全名',
                                        zh_TW: '',
                                        en_US: '',
                                      },
                                      label: {
                                        en_US: '',
                                        zh_CN: '客户全名',
                                        zh_TW: '',
                                        label: '客户全名',
                                      },
                                    },
                                    isFocusDisplay: false,
                                    id: '067CB836B3654AD588C9010DFF2348F4',
                                    sortable: true,
                                    filterable: true,
                                  },
                                ],
                                width: 160,
                                lang: {
                                  headerName: {
                                    en_US: '',
                                    zh_CN: '客户全名',
                                    zh_TW: '',
                                    label: '客户全名',
                                  },
                                },
                                id: '53E92E0E9681442591E5AD12FE10E6B8',
                              },
                              {
                                headerName: '银行账户',
                                path: 'customer_detail_bank',
                                level: 0,
                                columns: [
                                  {
                                    type: 'INPUT',
                                    schema: 'bank_account',
                                    path: 'customer_detail_bank',
                                    dataType: 'string',
                                    editable: false,
                                    disabled: true,
                                    headerName: '银行账户',
                                    label: '银行账户',
                                    placeholder: '银行账户',
                                    lang: {
                                      headerName: {
                                        en_US: '',
                                        zh_CN: '银行账户',
                                        zh_TW: '',
                                        label: '银行账户',
                                      },
                                      placeholder: {
                                        label: '银行账户',
                                        zh_CN: '银行账户',
                                        zh_TW: '',
                                        en_US: '',
                                      },
                                      label: {
                                        en_US: '',
                                        zh_CN: '银行账户',
                                        zh_TW: '',
                                        label: '银行账户',
                                      },
                                    },
                                    isFocusDisplay: false,
                                    id: 'C258D66017AA488987085C69B563548D',
                                    sortable: true,
                                    filterable: true,
                                  },
                                ],
                                width: 160,
                                lang: {
                                  headerName: {
                                    en_US: '',
                                    zh_CN: '银行账户',
                                    zh_TW: '',
                                    label: '银行账户',
                                  },
                                },
                                id: '374AC8DC0532458DB73553581284EADA',
                              },
                              {
                                headerName: '银行账户名称',
                                path: 'customer_detail_bank',
                                level: 0,
                                columns: [
                                  {
                                    type: 'INPUT',
                                    schema: 'bank_account_name',
                                    path: 'customer_detail_bank',
                                    dataType: 'string',
                                    editable: false,
                                    disabled: true,
                                    headerName: '银行账户名称',
                                    label: '银行账户名称',
                                    placeholder: '银行账户名称',
                                    lang: {
                                      headerName: {
                                        en_US: '',
                                        zh_CN: '银行账户名称',
                                        zh_TW: '',
                                        label: '银行账户名称',
                                      },
                                      placeholder: {
                                        label: '银行账户名称',
                                        zh_CN: '银行账户名称',
                                        zh_TW: '',
                                        en_US: '',
                                      },
                                      label: {
                                        en_US: '',
                                        zh_CN: '银行账户名称',
                                        zh_TW: '',
                                        label: '银行账户名称',
                                      },
                                    },
                                    isFocusDisplay: false,
                                    id: '3BEDCAD768EA4C32892D29336A12EFC2',
                                    sortable: true,
                                    filterable: true,
                                  },
                                ],
                                width: 160,
                                lang: {
                                  headerName: {
                                    en_US: '',
                                    zh_CN: '银行账户名称',
                                    zh_TW: '',
                                    label: '银行账户名称',
                                  },
                                },
                                id: 'B44E2EE4E5F6487FBC2ABA145BEFFE39',
                              },
                              {
                                headerName: '开户行名称',
                                path: 'customer_detail_bank',
                                level: 0,
                                columns: [
                                  {
                                    type: 'INPUT',
                                    schema: 'branch_name',
                                    path: 'customer_detail_bank',
                                    dataType: 'string',
                                    editable: false,
                                    disabled: true,
                                    headerName: '开户行名称',
                                    label: '开户行名称',
                                    placeholder: '开户行名称',
                                    lang: {
                                      headerName: {
                                        en_US: '',
                                        zh_CN: '开户行名称',
                                        zh_TW: '',
                                        label: '开户行名称',
                                      },
                                      placeholder: {
                                        label: '开户行名称',
                                        zh_CN: '开户行名称',
                                        zh_TW: '',
                                        en_US: '',
                                      },
                                      label: {
                                        en_US: '',
                                        zh_CN: '开户行名称',
                                        zh_TW: '',
                                        label: '开户行名称',
                                      },
                                    },
                                    isFocusDisplay: false,
                                    id: 'DD92FDBE51D446B99794EE9AF84AE277',
                                    sortable: true,
                                    filterable: true,
                                  },
                                ],
                                width: 160,
                                lang: {
                                  headerName: {
                                    en_US: '',
                                    zh_CN: '开户行名称',
                                    zh_TW: '',
                                    label: '开户行名称',
                                  },
                                },
                                id: 'D100700853714157BC21979066BBB16A',
                              },
                            ],
                            allFields: [],
                            setting: {
                              orderFields: [],
                              hideDefaultToolbar: [],
                              options: [],
                            },
                            hideDefaultToolbar: [],
                            saveColumnsWidth: true,
                            suppressAutoAddRow: false,
                            disabledUserDefined: true,
                            tableTitle: '',
                            lang: {
                              tableTitle: {
                                zh_CN: '',
                                zh_TW: '',
                                en_US: '',
                              },
                            },
                            checkboxOperation: false,
                            openRowHeight: true,
                          },
                        ],
                      },
                      description: '',
                      lang: {
                        description: {
                          zh_CN: '',
                          zh_TW: '',
                          en_US: '',
                        },
                      },
                      navigationConfig: {
                        enable: false,
                        url: 'base-data-entry',
                        urlParams: {},
                      },
                    },
                  ],
                  fields: [
                    {
                      schema: 'opponent_account',
                      show: true,
                    },
                    {
                      schema: 'opponent_account_name',
                      show: true,
                    },
                    {
                      schema: 'opponent_branch_name',
                      show: false,
                    },
                  ],
                },
                {
                  id: '48a5609e-93b1-4c37-bd8d-b15c715bc072',
                  type: 'OPERATION_EDITOR',
                  headerName: '单选开窗',
                  placeholder: '请输入',
                  schema: 'opponent_account',
                  path: 'receipt_checking',
                  disabled: false,
                  editable: true,
                  isFocusDisplay: false,
                  showInput: false,
                  dataType: 'string',
                  lang: {
                    headerName: {
                      zh_CN: '单选开窗',
                      zh_TW: '單選開窗',
                      en_US: 'OPERATION EDITOR',
                    },
                    placeholder: {
                      zh_CN: '请输入',
                      zh_TW: '請輸入',
                      en_US: 'please enter',
                    },
                    extraContent: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                    tooltipTitle: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                  },
                },
              ],
            },
            {
              headerName: '摘要',
              width: 100,
              path: '',
              level: 0,
              lang: {
                headerName: {
                  en_US: '摘要',
                  zh_CN: '摘要',
                  zh_TW: '摘要',
                },
              },
              id: 'd6ff5b7c-cd32-47ad-a896-6a9885a36a31',
              columns: [
                {
                  id: '682d13f6-5810-4d7c-8eea-3853294ec7a8',
                  type: 'INPUT',
                  headerName: '文本输入',
                  placeholder: '请输入',
                  schema: 'usage',
                  path: 'receipt_checking',
                  disabled: false,
                  editable: true,
                  isFocusDisplay: false,
                  dataType: 'string',
                  tooltipMode: 'normal',
                  iconType: '',
                  lang: {
                    headerName: {
                      zh_CN: '文本输入',
                      zh_TW: '文本输入',
                      en_US: 'Input',
                    },
                    placeholder: {
                      zh_CN: '请输入',
                      zh_TW: '請輸入',
                      en_US: 'please enter',
                    },
                    extraContent: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                    tooltipTitle: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                  },
                },
                {
                  id: '7439e0e8-1e4c-4e56-81e1-8cfb03be0cd4',
                  type: 'INPUT',
                  headerName: '文本输入',
                  placeholder: '请输入',
                  schema: 'postscript',
                  path: 'receipt_checking',
                  disabled: false,
                  editable: true,
                  isFocusDisplay: false,
                  dataType: 'string',
                  tooltipMode: 'normal',
                  iconType: '',
                  lang: {
                    headerName: {
                      zh_CN: '文本输入',
                      zh_TW: '文本输入',
                      en_US: 'Input',
                    },
                    placeholder: {
                      zh_CN: '请输入',
                      zh_TW: '請輸入',
                      en_US: 'please enter',
                    },
                    extraContent: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                    tooltipTitle: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                  },
                },
              ],
            },
            {
              headerName: '银行流水号',
              width: 100,
              path: '',
              level: 0,
              lang: {
                headerName: {
                  en_US: '银行流水号',
                  zh_CN: '银行流水号',
                  zh_TW: '银行流水号',
                },
              },
              id: 'bf417fe5-1aa5-4fb2-bc46-699f9f99c203',
              columns: [
                {
                  id: 'fb0bd2f2-2127-43c2-ba28-6d0e6add2bfc',
                  type: 'INPUT',
                  headerName: '文本输入',
                  placeholder: '请输入',
                  schema: 'serial_no',
                  path: 'receipt_checking',
                  disabled: false,
                  editable: true,
                  isFocusDisplay: false,
                  dataType: 'string',
                  tooltipMode: 'normal',
                  iconType: '',
                  lang: {
                    headerName: {
                      zh_CN: '文本输入',
                      zh_TW: '文本输入',
                      en_US: 'Input',
                    },
                    placeholder: {
                      zh_CN: '请输入',
                      zh_TW: '請輸入',
                      en_US: 'please enter',
                    },
                    extraContent: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                    tooltipTitle: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                  },
                },
              ],
            },
            {
              headerName: '交易金额',
              width: 100,
              path: '',
              level: 0,
              lang: {
                headerName: {
                  en_US: '交易金额',
                  zh_CN: '交易金额',
                  zh_TW: '交易金额',
                },
              },
              id: '10dbcae8-0cc2-4800-aa26-2651101220f0',
              columns: [
                {
                  id: 'a9078e63-f759-4737-b18c-f0ea17b4e7d7',
                  type: 'INPUT',
                  headerName: '数字输入',
                  placeholder: '请输入',
                  schema: 'trans_amount',
                  path: 'receipt_checking',
                  disabled: false,
                  editable: true,
                  isFocusDisplay: false,
                  dataType: 'numeric',
                  tooltipMode: 'normal',
                  iconType: '',
                  dataPrecision: {
                    length: '',
                    place: '',
                  },
                  max: '',
                  min: '',
                  step: 1,
                  lang: {
                    headerName: {
                      zh_CN: '数字输入',
                      zh_TW: '數字輸入',
                      en_US: 'Input',
                    },
                    placeholder: {
                      zh_CN: '请输入',
                      zh_TW: '請輸入',
                      en_US: 'please enter',
                    },
                    extraContent: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                    tooltipTitle: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                  },
                },
              ],
            },
            {
              headerName: '交易对象',
              width: 160,
              path: '',
              level: 0,
              lang: {
                headerName: {
                  en_US: '交易对象',
                  zh_CN: '交易对象',
                  zh_TW: '交易对象',
                },
              },
              id: '2708e53b-0332-430e-9e95-529d82da04a2',
              columns: [
                {
                  id: '5b148c39-796c-45c7-a9f4-247f55f56ffb',
                  type: 'OPERATION_EDITOR',
                  headerName: '单选开窗',
                  placeholder: '请输入',
                  schema: '',
                  path: '',
                  disabled: false,
                  editable: true,
                  isFocusDisplay: false,
                  showInput: false,
                  dataType: 'string',
                  lang: {
                    headerName: {
                      zh_CN: '单选开窗',
                      zh_TW: '單選開窗',
                      en_US: 'OPERATION EDITOR',
                    },
                    placeholder: {
                      zh_CN: '请输入',
                      zh_TW: '請輸入',
                      en_US: 'please enter',
                    },
                    extraContent: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                    tooltipTitle: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                  },
                  operations: [
                    {
                      attach: {
                        mode: 'row',
                      },
                      sequence: 0,
                      operate: 'openwindow',
                      name: '开窗',
                      id: '6536e023-3ab9-48f7-bff1-edf7859b3562',
                      openWindowDefine: {
                        title: '客户资料开窗',
                        lang: {
                          title: {
                            en_US: '客户资料开窗',
                            zh_CN: '客户资料开窗',
                            zh_TW: '客户资料开窗',
                          },
                        },
                        allAction: {
                          searchInfos: [],
                        },
                        multipleSelect: false,
                        applyToArray: false,
                        supportBatch: false,
                        selectedFirstRow: false,
                        useHasNext: true,
                        enableInputSearch: true,
                        buttons: [
                          {
                            id: 'confirm',
                            title: '提交',
                            lang: {
                              title: {
                                zh_TW: '提交',
                                en_US: 'Submit',
                                zh_CN: '提交',
                              },
                            },
                            language: {
                              title: {
                                zh_TW: '提交',
                                en_US: 'Submit',
                                zh_CN: '提交',
                              },
                            },
                            actions: [
                              {
                                backFills: [
                                  {
                                    key: 'trading_partner',
                                    valueScript: "selectedObject['customer_no']",
                                  },
                                  {
                                    key: 'trading_partner_name',
                                    valueScript: "selectedObject['customer_full_name']",
                                  },
                                ],
                                type: 'UI',
                              },
                            ],
                          },
                        ],
                        type: 'dataConnector',
                        dataConnectorId: 'customer_detail',
                        layout: [
                          {
                            type: 'ATHENA_TABLE',
                            path: '',
                            dataType: 'array',
                            checkbox: false,
                            rowDelete: false,
                            rowIndex: false,
                            rowSpanTree: false,
                            schema: '',
                            id: '0F36B973A59F4A49A857856AECB37CB5',
                            columnDefs: [
                              {
                                headerName: '客户编号',
                                path: 'customer_detail',
                                level: 0,
                                columns: [
                                  {
                                    type: 'INPUT',
                                    schema: 'customer_no',
                                    path: 'customer_detail',
                                    dataType: 'string',
                                    editable: false,
                                    disabled: true,
                                    headerName: '客户编号',
                                    label: '客户编号',
                                    placeholder: '客户编号',
                                    lang: {
                                      headerName: {
                                        en_US: '',
                                        zh_CN: '客户编号',
                                        zh_TW: '',
                                        label: '客户编号',
                                      },
                                      placeholder: {
                                        label: '客户编号',
                                        zh_CN: '客户编号',
                                        zh_TW: '',
                                        en_US: '',
                                      },
                                      label: {
                                        en_US: '',
                                        zh_CN: '客户编号',
                                        zh_TW: '',
                                        label: '客户编号',
                                      },
                                    },
                                    isFocusDisplay: false,
                                    id: 'EB906827851349B8ACD7B50A3CD2424F',
                                    sortable: true,
                                    filterable: true,
                                  },
                                ],
                                width: 160,
                                lang: {
                                  headerName: {
                                    en_US: '',
                                    zh_CN: '客户编号',
                                    zh_TW: '',
                                    label: '客户编号',
                                  },
                                },
                                id: 'C96E4CCA0EC24DF999368CF916EE4E3F',
                              },
                              {
                                headerName: '客户全名',
                                path: 'customer_detail',
                                level: 0,
                                columns: [
                                  {
                                    type: 'INPUT',
                                    schema: 'customer_full_name',
                                    path: 'customer_detail',
                                    dataType: 'string',
                                    editable: false,
                                    disabled: true,
                                    headerName: '客户全名',
                                    label: '客户全名',
                                    placeholder: '客户全名',
                                    lang: {
                                      headerName: {
                                        en_US: '',
                                        zh_CN: '客户全名',
                                        zh_TW: '',
                                        label: '客户全名',
                                      },
                                      placeholder: {
                                        label: '客户全名',
                                        zh_CN: '客户全名',
                                        zh_TW: '',
                                        en_US: '',
                                      },
                                      label: {
                                        en_US: '',
                                        zh_CN: '客户全名',
                                        zh_TW: '',
                                        label: '客户全名',
                                      },
                                    },
                                    isFocusDisplay: false,
                                    id: '8626A96973DB4FA5A8E2C5C4CE4BE06B',
                                    sortable: true,
                                    filterable: true,
                                  },
                                ],
                                width: 160,
                                lang: {
                                  headerName: {
                                    en_US: '',
                                    zh_CN: '客户全名',
                                    zh_TW: '',
                                    label: '客户全名',
                                  },
                                },
                                id: 'BDDF809842504FE88DE76B2772A53EE6',
                              },
                              {
                                headerName: '客户简称',
                                path: 'customer_detail',
                                level: 0,
                                columns: [
                                  {
                                    type: 'INPUT',
                                    schema: 'customer_shortname',
                                    path: 'customer_detail',
                                    dataType: 'string',
                                    editable: false,
                                    disabled: true,
                                    headerName: '客户简称',
                                    label: '客户简称',
                                    placeholder: '客户简称',
                                    lang: {
                                      headerName: {
                                        en_US: '',
                                        zh_CN: '客户简称',
                                        zh_TW: '',
                                        label: '客户简称',
                                      },
                                      placeholder: {
                                        label: '客户简称',
                                        zh_CN: '客户简称',
                                        zh_TW: '',
                                        en_US: '',
                                      },
                                      label: {
                                        en_US: '',
                                        zh_CN: '客户简称',
                                        zh_TW: '',
                                        label: '客户简称',
                                      },
                                    },
                                    isFocusDisplay: false,
                                    id: 'E4CD8C2E8DCB4BC2AA70804D57925493',
                                    sortable: true,
                                    filterable: true,
                                  },
                                ],
                                width: 160,
                                lang: {
                                  headerName: {
                                    en_US: '',
                                    zh_CN: '客户简称',
                                    zh_TW: '',
                                    label: '客户简称',
                                  },
                                },
                                id: '8E3EF901C5E24ED0B61B8875847A94EA',
                              },
                            ],
                            allFields: [],
                            setting: {
                              orderFields: [],
                              hideDefaultToolbar: [],
                              options: [],
                            },
                            hideDefaultToolbar: [],
                            saveColumnsWidth: true,
                            suppressAutoAddRow: false,
                            disabledUserDefined: true,
                            tableTitle: '',
                            lang: {
                              tableTitle: {
                                zh_CN: '',
                                zh_TW: '',
                                en_US: '',
                              },
                            },
                            checkboxOperation: false,
                            openRowHeight: true,
                          },
                        ],
                      },
                      description: '',
                      lang: {
                        description: {
                          zh_CN: '',
                          zh_TW: '',
                          en_US: '',
                        },
                      },
                      navigationConfig: {
                        enable: false,
                        url: 'base-data-entry',
                        urlParams: {},
                      },
                    },
                  ],
                  fields: [
                    {
                      schema: 'trading_partner',
                      show: false,
                    },
                    {
                      schema: 'trading_partner_name',
                      show: true,
                    },
                  ],
                },
              ],
            },
            {
              headerName: '交易性质',
              width: 100,
              path: '',
              level: 0,
              lang: {
                headerName: {
                  en_US: '交易性质',
                  zh_CN: '交易性质',
                  zh_TW: '交易性质',
                },
              },
              id: '7f863785-e3ea-4dfb-b8b3-b61a8758b2bb',
              columns: [
                {
                  id: '1d0b0938-6a00-4d9e-a211-6e6c3aeae601',
                  type: 'SELECT',
                  headerName: '单选开窗',
                  placeholder: '请输入',
                  schema: '',
                  path: '',
                  disabled: false,
                  editable: true,
                  isFocusDisplay: false,
                  dataType: 'string',
                  tooltipMode: 'normal',
                  iconType: '',
                  options: [
                    {
                      lang: {
                        title: {
                          zh_CN: '客户收款',
                          en_US: '客户收款',
                          zh_TW: '客户收款',
                        },
                      },
                      value: '0',
                      title: '客户收款',
                    },
                  ],
                  lang: {
                    headerName: {
                      zh_CN: '单选开窗',
                      zh_TW: '單選開窗',
                      en_US: 'OPERATION EDITOR',
                    },
                    placeholder: {
                      zh_CN: '请输入',
                      zh_TW: '請輸入',
                      en_US: 'please enter',
                    },
                    extraContent: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                    tooltipTitle: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                  },
                  dataConnectorId: '',
                  optionsType: 'dataConnector',
                },
              ],
            },
            {
              headerName: '款别',
              width: 100,
              path: '',
              level: 0,
              lang: {
                headerName: {
                  en_US: '款别',
                  zh_CN: '款别',
                  zh_TW: '款别',
                },
              },
              id: '69d7a57d-0bb4-4d87-a0dd-3f789ee6819c',
              columns: [
                {
                  id: '867f439a-698b-4e2e-b139-c2dc12d6d6b8',
                  type: 'OPERATION_EDITOR',
                  headerName: '单选开窗',
                  placeholder: '请输入',
                  schema: '',
                  path: '',
                  disabled: false,
                  editable: true,
                  isFocusDisplay: false,
                  showInput: false,
                  dataType: 'string',
                  lang: {
                    headerName: {
                      zh_CN: '单选开窗',
                      zh_TW: '單選開窗',
                      en_US: 'OPERATION EDITOR',
                    },
                    placeholder: {
                      zh_CN: '请输入',
                      zh_TW: '請輸入',
                      en_US: 'please enter',
                    },
                    extraContent: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                    tooltipTitle: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                  },
                  operations: [
                    {
                      attach: {
                        mode: 'row',
                      },
                      sequence: 0,
                      operate: 'openwindow',
                      name: '开窗',
                      id: '991939eb-2d69-4951-9e14-2ca8f82672bf',
                      openWindowDefine: {
                        title: '款别资料',
                        lang: {
                          title: {
                            en_US: '款别资料',
                            zh_CN: '款别资料',
                            zh_TW: '款别资料',
                          },
                        },
                        allAction: {
                          searchInfos: [],
                        },
                        multipleSelect: false,
                        applyToArray: false,
                        supportBatch: false,
                        selectedFirstRow: false,
                        useHasNext: true,
                        enableInputSearch: true,
                        buttons: [
                          {
                            id: 'confirm',
                            title: '提交',
                            lang: {
                              title: {
                                zh_TW: '提交',
                                en_US: 'Submit',
                                zh_CN: '提交',
                              },
                            },
                            language: {
                              title: {
                                zh_TW: '提交',
                                en_US: 'Submit',
                                zh_CN: '提交',
                              },
                            },
                            actions: [
                              {
                                backFills: [
                                  {
                                    key: 'payment_type_code',
                                    valueScript: "selectedObject['payment_type_no']",
                                  },
                                  {
                                    key: 'payment_type_name',
                                    valueScript: "selectedObject['payment_type_name']",
                                  },
                                ],
                                type: 'UI',
                              },
                            ],
                          },
                        ],
                        type: 'dataConnector',
                        dataConnectorId: 'payment_type',
                        layout: [
                          {
                            type: 'ATHENA_TABLE',
                            path: '',
                            dataType: 'array',
                            checkbox: false,
                            rowDelete: false,
                            rowIndex: false,
                            rowSpanTree: false,
                            schema: '',
                            id: '064BE00E13834F2295AECD585CD9E996',
                            columnDefs: [
                              {
                                headerName: '款别编号',
                                path: 'payment_type',
                                level: 0,
                                columns: [
                                  {
                                    type: 'INPUT',
                                    schema: 'payment_type_no',
                                    path: 'payment_type',
                                    dataType: 'string',
                                    editable: false,
                                    disabled: true,
                                    headerName: '款别编号',
                                    label: '款别编号',
                                    placeholder: '款别编号',
                                    lang: {
                                      headerName: {
                                        en_US: '',
                                        zh_CN: '款别编号',
                                        zh_TW: '',
                                        label: '款别编号',
                                      },
                                      placeholder: {
                                        label: '款别编号',
                                        zh_CN: '款别编号',
                                        zh_TW: '',
                                        en_US: '',
                                      },
                                      label: {
                                        en_US: '',
                                        zh_CN: '款别编号',
                                        zh_TW: '',
                                        label: '款别编号',
                                      },
                                    },
                                    isFocusDisplay: false,
                                    id: '56FC14CCC7AF4B2FBC521930E1348017',
                                    sortable: true,
                                    filterable: true,
                                  },
                                ],
                                width: 160,
                                lang: {
                                  headerName: {
                                    en_US: '',
                                    zh_CN: '款别编号',
                                    zh_TW: '',
                                    label: '款别编号',
                                  },
                                },
                                id: '5B1E482C83F64CE9B8EAB19616E880F5',
                              },
                              {
                                headerName: '款别说明',
                                path: 'payment_type',
                                level: 0,
                                columns: [
                                  {
                                    type: 'INPUT',
                                    schema: 'payment_type_name',
                                    path: 'payment_type',
                                    dataType: 'string',
                                    editable: false,
                                    disabled: true,
                                    headerName: '款别说明',
                                    label: '款别说明',
                                    placeholder: '款别说明',
                                    lang: {
                                      headerName: {
                                        en_US: '',
                                        zh_CN: '款别说明',
                                        zh_TW: '',
                                        label: '款别说明',
                                      },
                                      placeholder: {
                                        label: '款别说明',
                                        zh_CN: '款别说明',
                                        zh_TW: '',
                                        en_US: '',
                                      },
                                      label: {
                                        en_US: '',
                                        zh_CN: '款别说明',
                                        zh_TW: '',
                                        label: '款别说明',
                                      },
                                    },
                                    isFocusDisplay: false,
                                    id: 'CF53A1E910D845C3A9E7A2AF47970FDE',
                                    sortable: true,
                                    filterable: true,
                                  },
                                ],
                                width: 160,
                                lang: {
                                  headerName: {
                                    en_US: '',
                                    zh_CN: '款别说明',
                                    zh_TW: '',
                                    label: '款别说明',
                                  },
                                },
                                id: '7A113429E93C4E159315C58F91A6C6BB',
                              },
                              {
                                headerName: '款别性质',
                                path: 'payment_type',
                                level: 0,
                                columns: [
                                  {
                                    type: 'INPUT',
                                    schema: 'payment_type_nature',
                                    path: 'payment_type',
                                    dataType: 'string',
                                    editable: false,
                                    disabled: true,
                                    headerName: '款别性质',
                                    label: '款别性质',
                                    placeholder: '款别性质',
                                    lang: {
                                      headerName: {
                                        en_US: '',
                                        zh_CN: '款别性质',
                                        zh_TW: '',
                                        label: '款别性质',
                                      },
                                      placeholder: {
                                        label: '款别性质',
                                        zh_CN: '款别性质',
                                        zh_TW: '',
                                        en_US: '',
                                      },
                                      label: {
                                        en_US: '',
                                        zh_CN: '款别性质',
                                        zh_TW: '',
                                        label: '款别性质',
                                      },
                                    },
                                    isFocusDisplay: false,
                                    id: '1CFE9A68ABD9457F8B702024A169B365',
                                    sortable: true,
                                    filterable: true,
                                  },
                                ],
                                width: 160,
                                lang: {
                                  headerName: {
                                    en_US: '',
                                    zh_CN: '款别性质',
                                    zh_TW: '',
                                    label: '款别性质',
                                  },
                                },
                                id: '4BB4A0944010460AB188EC362FEDCDC4',
                              },
                            ],
                            allFields: [],
                            setting: {
                              orderFields: [],
                              hideDefaultToolbar: [],
                              options: [],
                            },
                            hideDefaultToolbar: [],
                            saveColumnsWidth: true,
                            suppressAutoAddRow: false,
                            disabledUserDefined: true,
                            tableTitle: '',
                            lang: {
                              tableTitle: {
                                zh_CN: '',
                                zh_TW: '',
                                en_US: '',
                              },
                            },
                            checkboxOperation: false,
                            openRowHeight: true,
                          },
                        ],
                      },
                      description: '',
                      lang: {
                        description: {
                          zh_CN: '',
                          zh_TW: '',
                          en_US: '',
                        },
                      },
                      navigationConfig: {
                        enable: false,
                        url: 'base-data-entry',
                        urlParams: {},
                      },
                    },
                  ],
                  fields: [
                    {
                      schema: 'payment_type_code',
                      show: false,
                    },
                    {
                      schema: 'payment_type_name',
                      show: true,
                    },
                  ],
                },
              ],
            },
            {
              headerName: '存提码',
              width: 100,
              path: '',
              level: 0,
              lang: {
                headerName: {
                  en_US: '存提码',
                  zh_CN: '存提码',
                  zh_TW: '存提码',
                },
              },
              id: '6e054594-cfe6-4529-9345-0ceb74d33778',
              columns: [
                {
                  id: 'a84d7e54-aee7-4322-aba5-2447c99988a3',
                  type: 'OPERATION_EDITOR',
                  headerName: '单选开窗',
                  placeholder: '请输入',
                  schema: '',
                  path: '',
                  disabled: false,
                  editable: true,
                  isFocusDisplay: false,
                  showInput: false,
                  dataType: 'string',
                  lang: {
                    headerName: {
                      zh_CN: '单选开窗',
                      zh_TW: '單選開窗',
                      en_US: 'OPERATION EDITOR',
                    },
                    placeholder: {
                      zh_CN: '请输入',
                      zh_TW: '請輸入',
                      en_US: 'please enter',
                    },
                    extraContent: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                    tooltipTitle: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                  },
                  operations: [
                    {
                      attach: {
                        mode: 'row',
                      },
                      sequence: 0,
                      operate: 'openwindow',
                      name: '开窗',
                      id: '9aaf10d6-8400-4a28-a151-ca4e2dbc87c8',
                      openWindowDefine: {
                        title: '存提码资料',
                        lang: {
                          title: {
                            en_US: '存提码资料',
                            zh_CN: '存提码资料',
                            zh_TW: '存提码资料',
                          },
                        },
                        allAction: {
                          searchInfos: [],
                        },
                        multipleSelect: false,
                        applyToArray: false,
                        supportBatch: false,
                        selectedFirstRow: false,
                        useHasNext: true,
                        enableInputSearch: true,
                        buttons: [
                          {
                            id: 'confirm',
                            title: '提交',
                            lang: {
                              title: {
                                zh_TW: '提交',
                                en_US: 'Submit',
                                zh_CN: '提交',
                              },
                            },
                            language: {
                              title: {
                                zh_TW: '提交',
                                en_US: 'Submit',
                                zh_CN: '提交',
                              },
                            },
                            actions: [
                              {
                                backFills: [
                                  {
                                    key: 'deposit_info',
                                    valueScript: "selectedObject['deposit_withdraw_code']",
                                  },
                                  {
                                    key: 'deposit_info_name',
                                    valueScript: "selectedObject['deposit_withdraw_code_desc']",
                                  },
                                  {
                                    key: 'cash_change_code',
                                    valueScript: "selectedObject['cash_change_no']",
                                  },
                                  {
                                    key: 'cash_change_name',
                                    valueScript: "selectedObject['cash_change_code']",
                                  },
                                ],
                                type: 'UI',
                              },
                            ],
                          },
                        ],
                        type: 'dataConnector',
                        dataConnectorId: 'deposit_withdraw_code_info',
                        layout: [
                          {
                            type: 'ATHENA_TABLE',
                            path: '',
                            dataType: 'array',
                            checkbox: false,
                            rowDelete: false,
                            rowIndex: false,
                            rowSpanTree: false,
                            schema: '',
                            id: 'B983B5D438804E0BB5FB7E5F3368F89A',
                            columnDefs: [
                              {
                                headerName: '存提码',
                                path: 'deposit_withdraw_code_info',
                                level: 0,
                                columns: [
                                  {
                                    type: 'INPUT',
                                    schema: 'deposit_withdraw_code',
                                    path: 'deposit_withdraw_code_info',
                                    dataType: 'string',
                                    editable: false,
                                    disabled: true,
                                    headerName: '存提码',
                                    label: '存提码',
                                    placeholder: '存提码',
                                    lang: {
                                      headerName: {
                                        en_US: '',
                                        zh_CN: '存提码',
                                        zh_TW: '',
                                        label: '存提码',
                                      },
                                      placeholder: {
                                        label: '存提码',
                                        zh_CN: '存提码',
                                        zh_TW: '',
                                        en_US: '',
                                      },
                                      label: {
                                        en_US: '',
                                        zh_CN: '存提码',
                                        zh_TW: '',
                                        label: '存提码',
                                      },
                                    },
                                    isFocusDisplay: false,
                                    id: 'EFC913126B734F0CA09E92483233C99C',
                                    sortable: true,
                                    filterable: true,
                                  },
                                ],
                                width: 160,
                                lang: {
                                  headerName: {
                                    en_US: '',
                                    zh_CN: '存提码',
                                    zh_TW: '',
                                    label: '存提码',
                                  },
                                },
                                id: '8392F57F2B6D448187E079B40715CC4F',
                              },
                              {
                                headerName: '存提码说明',
                                path: 'deposit_withdraw_code_info',
                                level: 0,
                                columns: [
                                  {
                                    type: 'INPUT',
                                    schema: 'deposit_withdraw_code_desc',
                                    path: 'deposit_withdraw_code_info',
                                    dataType: 'string',
                                    editable: false,
                                    disabled: true,
                                    headerName: '存提码说明',
                                    label: '存提码说明',
                                    placeholder: '存提码说明',
                                    lang: {
                                      headerName: {
                                        en_US: '',
                                        zh_CN: '存提码说明',
                                        zh_TW: '',
                                        label: '存提码说明',
                                      },
                                      placeholder: {
                                        label: '存提码说明',
                                        zh_CN: '存提码说明',
                                        zh_TW: '',
                                        en_US: '',
                                      },
                                      label: {
                                        en_US: '',
                                        zh_CN: '存提码说明',
                                        zh_TW: '',
                                        label: '存提码说明',
                                      },
                                    },
                                    isFocusDisplay: false,
                                    id: '202922D39BEA4CA39BB0AB7DAF4D96D4',
                                    sortable: true,
                                    filterable: true,
                                  },
                                ],
                                width: 160,
                                lang: {
                                  headerName: {
                                    en_US: '',
                                    zh_CN: '存提码说明',
                                    zh_TW: '',
                                    label: '存提码说明',
                                  },
                                },
                                id: '0CA9A14BA1434FEA870F7FEDC74FFC03',
                              },
                            ],
                            allFields: [],
                            setting: {
                              orderFields: [],
                              hideDefaultToolbar: [],
                              options: [],
                            },
                            hideDefaultToolbar: [],
                            saveColumnsWidth: true,
                            suppressAutoAddRow: false,
                            disabledUserDefined: true,
                            tableTitle: '',
                            lang: {
                              tableTitle: {
                                zh_CN: '',
                                zh_TW: '',
                                en_US: '',
                              },
                            },
                            checkboxOperation: false,
                            openRowHeight: true,
                          },
                        ],
                      },
                      description: '',
                      lang: {
                        description: {
                          zh_CN: '',
                          zh_TW: '',
                          en_US: '',
                        },
                      },
                      navigationConfig: {
                        enable: false,
                        url: 'base-data-entry',
                        urlParams: {},
                      },
                    },
                  ],
                  fields: [
                    {
                      schema: 'deposit_info',
                      show: false,
                    },
                    {
                      schema: 'deposit_info_name',
                      show: true,
                    },
                    {
                      schema: 'cash_change_code',
                      show: false,
                    },
                    {
                      schema: 'cash_change_name',
                      show: true,
                    },
                  ],
                },
              ],
            },
            {
              headerName: '现金变动码',
              width: 100,
              path: '',
              level: 0,
              lang: {
                headerName: {
                  en_US: '现金变动码',
                  zh_CN: '现金变动码',
                  zh_TW: '现金变动码',
                },
              },
              id: 'd01af0b0-ca1a-4792-bdb1-d6b816faed93',
              columns: [
                {
                  id: '7dceed95-a664-45d6-b2c8-d9344bcdb79c',
                  type: 'OPERATION_EDITOR',
                  headerName: '单选开窗',
                  placeholder: '请输入',
                  schema: '',
                  path: '',
                  disabled: false,
                  editable: true,
                  isFocusDisplay: false,
                  showInput: false,
                  dataType: 'string',
                  lang: {
                    headerName: {
                      zh_CN: '单选开窗',
                      zh_TW: '單選開窗',
                      en_US: 'OPERATION EDITOR',
                    },
                    placeholder: {
                      zh_CN: '请输入',
                      zh_TW: '請輸入',
                      en_US: 'please enter',
                    },
                    extraContent: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                    tooltipTitle: {
                      zh_CN: '',
                      zh_TW: '',
                      en_US: '',
                    },
                  },
                },
              ],
            },
            {
              headerName: '表格附件',
              width: 100,
              path: '',
              level: 0,
              lang: {
                headerName: {
                  zh_CN: '表格附件',
                  zh_TW: '表格附檔',
                  en_US: 'file upload',
                },
              },
              id: '78ca15f1-b878-419e-ab6a-6f541fa6e6d3',
              columns: [
                {
                  id: '2311ef8f-5a37-43e4-8672-cefe74a1f129',
                  type: 'FILE_UPLOAD_DECOUPLE',
                  headerName: '表格附件',
                  placeholder: '',
                  schema: '',
                  path: '',
                  dataType: 'object',
                  lang: {
                    headerName: {
                      zh_CN: '表格附件',
                      zh_TW: '表格附檔',
                      en_US: 'file upload',
                    },
                  },
                  apiMode: 'dataConnector',
                  buckets: '',
                  attribute: {
                    uploadEnable: false,
                    uploadCategory: '',
                    fileExtensions: [],
                    fileCount: '',
                    fileMaxSize: '',
                    draggable: false,
                    disableAam: true,
                    enableEffectAfterSubmit: false,
                    onlyDeleteByOwner: false,
                    filterKey: '',
                  },
                },
              ],
            },
            {
              headerName: '操作',
              width: 160,
              level: 0,
              lang: {
                headerName: {
                  zh_CN: '操作',
                },
              },
              id: 'bd5c684e-b30b-4868-a2df-a13c20048113',
              pinned: 'right',
              columns: [
                {
                  id: 'b7f70769-f245-485e-b8a9-9761cd446785',
                  type: 'BUTTON_DELETE_ITEM_DECOUPLE',
                  title: '删除',
                  lang: {
                    title: {
                      zh_CN: '删除',
                      zh_TW: '删除',
                      en_US: 'Delete',
                    },
                  },
                  styleMode: 'text',
                  size: 'small',
                  disabled: false,
                  ghost: false,
                  danger: false,
                  block: false,
                  debounce: false,
                  showLoading: false,
                  async: false,
                  debounceTime: 300,
                  targetSchema: 'receipt_checking_full',
                  targetPath: '',
                  attachMode: 'row',
                  operation: {
                    dataSourceType: 'dataConnector',
                    dataConnectorId: '',
                    extendedFields: null,
                  },
                },
              ],
            },
          ],
        },
      ],
      hooks: [],
      rules: [],
      variables: [],
      dataConnectors: [],
      globalSetting: {},
    },
  },
];
