import { Converter, DslData, AthComponentType, ConvertOutput, DslSchema } from '../type';

export const athenaTableAthConverter: Converter = {
  key: AthComponentType.ATHENA_TABLE,
  toSchema: (dsl: DslData): ConvertOutput<DslSchema, DslData> => {
    // dsl中的属性剔除子层级后将作为dslInfo塞到props中
    // 所以dslInfo就是原始的dsl数据，每个具体渲染的node节点都会维护各自的dslInfo
    // columnDefs 就是ATHENA_TABLE dsl中 子层级的key
    // 然后childrenData是个数组，如果像layout组件，分别有header，sider，content三个位置的子层级
    // childrenData数组里就会有3条
    const { columnDefs = [], slots, ...dslInfo } = dsl;

    // slots 转换为 动态操作
    const slotsSchema = slots?.map((item: DslData) => {
      return {
        ...item,
        type: AthComponentType.DYNAMIC_OPERATION,
      };
    }) || [
      {
        type: AthComponentType.DYNAMIC_OPERATION,
      },
    ];

    // columnDefs 转换为 TABLE_GROUP
    const columnDefsSchema =
      columnDefs?.map((item: DslData) => {
        // 这段就是针对 dsl 的 特殊逻辑
        // columnDefs 下一层 并不直接 是 子组件list，其实是一个 TABLE_GROUP list
        // 但是 dsl 中 并没有类型，而lowcode 中 是作为TABLE_GROUP组件 去渲染的
        // 所以 在这里 补上 类型
        return {
          ...item,
          type: AthComponentType.TABLE_GROUP,
        };
      }) || [];

    return {
      data: {
        componentName: AthComponentType.ATHENA_TABLE,
        props: { dslInfo },
      },
      childrenData: [
        {
          // 在这里，其实lowcode的子层级都是标准的children，但为了结构对称，这里也可以配置
          // children中都是未经过转换的dsl数据，return出去交由其他转换器递归处理
          // 最后将转换后的子层级 schema数据塞到对应的子层级 key中，比如这里的columnDefs
          key: 'children',
          data: [...slotsSchema, ...columnDefsSchema],
        },
      ],
    };
  },

  toDsl: (dslSchema: DslSchema): ConvertOutput<DslData, DslSchema> => {
    const { props, children = [] } = dslSchema;

    const { sortChildren, otherChildren } = children.reduce<{
      sortChildren: any[];
      otherChildren: any[];
    }>(
      (acc, cur) => {
        if (cur.componentName === AthComponentType.DYNAMIC_OPERATION) {
          acc.sortChildren.push(cur);
        } else {
          acc.otherChildren.push(cur);
        }
        return acc;
      },
      { sortChildren: [], otherChildren: [] },
    );

    return {
      data: {
        ...props.dslInfo,
        type: AthComponentType.ATHENA_TABLE,
      },
      childrenData: [
        {
          key: 'slots',
          data: sortChildren,
        },
        {
          key: 'columnDefs',
          data: otherChildren,
        },
      ],
    };
  },

  valid: (dsl: DslData) => {
    return true;
  },
};
