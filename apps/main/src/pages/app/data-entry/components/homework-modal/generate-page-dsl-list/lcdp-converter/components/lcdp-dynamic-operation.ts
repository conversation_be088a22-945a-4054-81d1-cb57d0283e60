import { Converter, DslData, AthComponentType, DslSchema } from '../type';

export const lcdpDynamicOperationConverter: Converter = {
  key: AthComponentType.DYNAMIC_OPERATION,
  toSchema: (dsl: DslData) => {
    const { group = [], ...dslInfo } = dsl;
    return {
      data: {
        componentName: AthComponentType.DYNAMIC_OPERATION,
        props: { dslInfo },
      },
      childrenData: [
        {
          key: 'children',
          data: group,
        },
      ],
    };
  },

  toDsl: (schema: DslSchema) => {
    const { props, children = [] } = schema;
    const { dslInfo } = props ?? {};
    const { type, id, ...rest } = dslInfo ?? {};
    return {
      data: {
        ...rest,
      },
      childrenData: [
        {
          key: 'group',
          data: children,
        },
      ],
    };
  },

  valid: () => {
    return true;
  },
};
