import { Converter, DslData, AthComponentType, ConvertOutput, DslSchema } from '../type';

export const LcdpFormListConverter: Converter = {
  key: AthComponentType.FORM_LIST,
  toSchema: (dsl: DslData): ConvertOutput<DslSchema, DslData> => {
    const { group = [], slots, ...dslInfo } = dsl;

    // slots 转换为 动态操作
    const slotsSchema = slots?.map((item: DslData) => {
      return {
        ...item,
        type: AthComponentType.DYNAMIC_OPERATION,
      };
    }) || [
      {
        type: AthComponentType.DYNAMIC_OPERATION,
      },
    ];

    return {
      data: {
        componentName: AthComponentType.FORM_LIST,
        props: { dslInfo },
      },
      childrenData: [
        {
          key: 'children',
          data: [...slotsSchema, ...group],
        },
      ],
    };
  },

  toDsl: (dslSchema: DslSchema): ConvertOutput<DslData, DslSchema> => {
    const { props, children = [] } = dslSchema;

    const { sortChildren, otherChildren } = children.reduce<{
      sortChildren: any[];
      otherChildren: any[];
    }>(
      (acc, cur) => {
        if (cur.componentName === AthComponentType.DYNAMIC_OPERATION) {
          acc.sortChildren.push(cur);
        } else {
          acc.otherChildren.push(cur);
        }
        return acc;
      },
      { sortChildren: [], otherChildren: [] },
    );

    return {
      data: {
        ...props.dslInfo,
        type: AthComponentType.FORM_LIST,
      },
      childrenData: [
        {
          key: 'slots',
          data: sortChildren,
        },
        {
          key: 'group',
          data: otherChildren,
        },
      ],
    };
  },

  valid: (dsl: DslData) => {
    return true;
  },
};
