import { Converter, DslData, AthComponentType, ConvertOutput, DslSchema } from '../type';
import { omit } from 'lodash';

export const tableGroupAthConverter: Converter = {
  key: AthComponentType.TABLE_GROUP,
  toSchema: (dsl: DslData): ConvertOutput<DslSchema, DslData> => {
    const { columns = [], ...dslInfo } = dsl;
    return {
      data: {
        componentName: AthComponentType.TABLE_GROUP,
        props: { dslInfo },
      },
      childrenData: [
        {
          key: 'children',
          data: columns,
        },
      ],
    };
  },

  toDsl: (dslSchema: DslSchema): ConvertOutput<DslData, DslSchema> => {
    const { props, children = [] } = dslSchema;
    return {
      // 由于dsl中没有TABLE_GROUP，所以在这里需要将type移除，但其他属性保留
      data: omit(props.dslInfo, ['type']),
      childrenData: [
        {
          key: 'columns',
          data: children,
        },
      ],
    };
  },

  valid: (dsl: DslData) => {
    return true;
  },
};
