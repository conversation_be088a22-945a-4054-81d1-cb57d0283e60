import { GenerateBusinessType } from '../../homework-modal.tool';
import { DataConnector, GenerateAthComponentType, AthenaDataType } from '../type';
import { editableTableDslList } from './editable-table.config';
import { basicTableDslList } from './basic-table.config';

export const dataConnectorTemplate: DataConnector = {
  id: '', // 内部使用,32位UUID
  name: 'data_name', // 使用名，唯一，取fieldData的data_name
  description: '用于创建新用户的API', // 取fieldData的description，支持多语言
  connectType: 'api', // 链接类型，目前仅有API
  runOnPageLoad: true, // 页面加载时立即请求数据，这里我们默认传true
  option: {
    request: {
      method: 'POST',
      path: '{{variables.system.sys_ipaas_url}}',
      params: [],
      body: {
        type: 'json',
        content: {
          std_data: {
            parameter: {
              enterprise_no: '{{variables.system.sys_c_ent}}',
              site_no: '{{variables.system.sys_c_org}}',
              is_digiwin_product: 'Y',
            },
          },
        },
      },
      headers: [
        {
          key: 'Content-Type',
          value: 'application/json; charset=utf-8',
        },
        {
          key: 'digi-protocol',
          value: 'raw',
        },
        {
          key: 'digi-type',
          value: 'sync',
        },
        {
          key: 'digi-host', // timestamp为空，后面在脚本中替换
          value: JSON.stringify({
            prod: '{{variables.system.sys_fx_prod}}',
            ip: '{{variables.system.sys_fx_ip}}',
            timestamp: '',
            acct: '{{variables.system.sys_c_user}}',
            lang: '{{variables.system.sys_c_lang}}',
          }),
        },
        {
          key: 'digi-service',
          value: {
            prod: '环境变量-digi-service-prod', // 来自于接口返回的数据
            name: '环境变量-digi-service-name', // 来自于接口返回的数据
            ip: '{{variables.system.sys_T100_ip}}',
            id: '{{variables.system.sys_T100_id}}',
            UID: '{{variables.system.sys_T100_uid}}',
          },
        },
        {
          key: 'digi-datakey',
          value: JSON.stringify({
            EntId: '{{variables.system.sys_c_ent}}',
            CompanyId: '{{variables.system.sys_c_org}}',
          }),
        },
      ],
      cookies: [],
    },
    preProcess: {
      type: 'javascript',
      script: `
                const { headers, ...rest } = request;
                const digi_host = headers['digi-host'];
                const digi_service = headers['digi-service'];
                const hostJson = JSON.parse(digi_host);
                hostJson.timestamp = new Date().getTime();
                headers['digi-host'] = JSON.stringify(hostJson);
                const digi_key = utils.md5(\`\${headers['digi-host']}\${digi_service}\`).toString();
                headers['digi-key'] = digi_key;
              `,
    },
    postProcess: {
      type: 'javascript',
      script: null,
    },
    // 仅设计时自用
    response: {
      type: 'Esp', // 默认Standard：标准树结构，Esp：原ESP数据字段结构
      meta: {}, // 后台返回的字段树数据
    },
  },
  lang: {
    // 先行添加，后面可能会用到
    description: {
      zh_CN: '用于创建新用户的API',
      zh_TW: '用於創建新用戶的API',
      en_US: 'API for creating new users',
    },
  },
};

// pageDsl模版map
export const pageDslTemplateMap = {
  [GenerateBusinessType.BASIC_TABLE]: basicTableDslList,
  [GenerateBusinessType.EDITABLE_TABLE]: editableTableDslList,
};

// 因为在这里实现无法获取到设计器的实际meta只能如此
export const baseGenerateAthComponentSchemaMap: any = {
  [GenerateAthComponentType.ATHENA_TABLE]: {
    componentName: GenerateAthComponentType.ATHENA_TABLE,
    props: {
      dslInfo: {
        tableTitle: '表格',
        lang: {
          tableTitle: {
            zh_CN: '表格',
            zh_TW: '表格',
            en_US: 'table',
          },
        },
        type: GenerateAthComponentType.ATHENA_TABLE,
        schema: '',
        path: '',
        isSort: true,
        checkbox: false,
        rowDelete: false,
        rowIndex: true,
        id: '',
        saveColumnsWidth: true,
        suppressAutoAddRow: false,
        // checkboxOperation: false,
        // height: 200,
        rowHeight: 10,
        adaptiveModel: 'default',
        rowSpanTree: false,
        queryInfo: {
          pageInfo: {
            pageSize: 50,
          },
          dataFilter: {
            dataSourceNames: [],
            apiCondition: {},
          },
          isAsync: true,
          searchInfo: [],
        },
        setting: {
          hideDefaultToolbar: ['row-height-tool'],
          options: [],
        },
      },
    },
  },
  [GenerateAthComponentType.FORM_LIST]: {
    componentName: GenerateAthComponentType.FORM_LIST,
    props: {
      dslInfo: {
        id: '',
        title: '表单',
        dataType: 'object',
        lang: {
          title: {
            zh_CN: '表单',
            en_US: 'form list',
            zh_TW: '表单',
          },
        },
        direction: 'ROW',
        type: GenerateAthComponentType.FORM_LIST,
        schema: '',
        path: '',
        collapse: false,
        queryInfo: {
          dataFilter: {
            dataSourceNames: [],
            apiCondition: {},
          },
          isAsync: true,
        },
      },
    },
  },
  [GenerateAthComponentType.SELECT]: {
    componentName: GenerateAthComponentType.SELECT,
    title: 'dj-下拉单选',
    props: {
      dslInfo: {
        id: '',
        type: GenerateAthComponentType.SELECT,
        headerName: '下拉单选',
        placeholder: '请选择',
        schema: '',
        path: '',
        disabled: false,
        editable: true,
        isFocusDisplay: false,
        dataType: AthenaDataType.STRING,
        tooltipMode: 'normal', // 有注释iconType的都要加
        iconType: '',
        options: [],
        lang: {
          headerName: {
            zh_CN: '下拉单选',
            zh_TW: '下拉單選',
            en_US: 'select',
          },
          placeholder: {
            zh_CN: '请选择',
            zh_TW: '請選擇',
            en_US: 'please select',
          },
          extraContent: {
            zh_CN: '',
            zh_TW: '',
            en_US: '',
          },
          tooltipTitle: {
            zh_CN: '',
            zh_TW: '',
            en_US: '',
          },
        },
      },
    },
  },
  [GenerateAthComponentType.INPUT]: {
    componentName: GenerateAthComponentType.INPUT,
    title: 'dj-文本输入',
    props: {
      dslInfo: {
        id: '',
        type: GenerateAthComponentType.INPUT,
        headerName: '文本输入',
        placeholder: '请输入',
        schema: '',
        path: '',
        disabled: false,
        editable: true,
        isFocusDisplay: false,
        dataType: AthenaDataType.STRING,
        tooltipMode: 'normal', // 有注释iconType的都要加
        iconType: '',
        lang: {
          headerName: {
            zh_CN: '文本输入',
            zh_TW: '文本输入',
            en_US: 'Input',
          },
          placeholder: {
            zh_CN: '请输入',
            zh_TW: '請輸入',
            en_US: 'please enter',
          },
          extraContent: {
            zh_CN: '',
            zh_TW: '',
            en_US: '',
          },
          tooltipTitle: {
            zh_CN: '',
            zh_TW: '',
            en_US: '',
          },
        },
      },
    },
  },
  [GenerateAthComponentType.INPUT_NUMBER]: {
    componentName: GenerateAthComponentType.INPUT,
    title: 'dj-数字输入',
    props: {
      dslInfo: {
        id: '',
        type: GenerateAthComponentType.INPUT,
        headerName: '数字输入',
        placeholder: '请输入',
        schema: '',
        path: '',
        disabled: false,
        editable: true,
        isFocusDisplay: false,
        dataType: AthenaDataType.NUMERIC,
        tooltipMode: 'normal', // 有注释iconType的都要加
        iconType: '',
        dataPrecision: {
          length: '',
          place: '',
        },
        max: '',
        min: '',
        step: 1,
        lang: {
          headerName: {
            zh_CN: '数字输入',
            zh_TW: '數字輸入',
            en_US: 'Input',
          },
          placeholder: {
            zh_CN: '请输入',
            zh_TW: '請輸入',
            en_US: 'please enter',
          },
          extraContent: {
            zh_CN: '',
            zh_TW: '',
            en_US: '',
          },
          tooltipTitle: {
            zh_CN: '',
            zh_TW: '',
            en_US: '',
          },
        },
      },
    },
  },
  [GenerateAthComponentType.DATEPICKER]: {
    componentName: GenerateAthComponentType.DATEPICKER,
    title: 'dj-日期选择',
    props: {
      dslInfo: {
        id: '',
        type: GenerateAthComponentType.DATEPICKER,
        headerName: '日期选择',
        placeholder: 'yyyyMMdd ',
        schema: '',
        path: '',
        disabled: false,
        editable: true,
        isFocusDisplay: false,
        fieldType: 'datetime',
        dataType: AthenaDataType.DATE,
        tooltipMode: 'normal', // 有注释iconType的都要加
        iconType: 'wenhao',
        weekStartsOn: 1, // 周日为一周的第一天，0表示周日，1表示周一，需要传给运行态
        mode: 'date',
        format: 'yyyy/MM/dd',
        formatConfig: {
          type: 'yyyy/MM/dd',
          value: 'yyyy/MM/dd',
        },
        showPickerOptions: false,
        pickerOptions: [],
        disabledDate: {
          type: 'none',
        },
        rules: { value: [] },
        lang: {
          headerName: {
            zh_CN: '日期选择',
            zh_TW: '日期選擇',
            en_US: 'DatePicker',
          },
          placeholder: {
            zh_CN: 'yyyyMMdd',
            zh_TW: 'yyyyMMdd',
            en_US: 'yyyyMMdd',
          },
          extraContent: {
            zh_CN: '',
            zh_TW: '',
            en_US: '',
          },
          tooltipTitle: {
            zh_CN: '',
            zh_TW: '',
            en_US: '',
          },
        },
      },
    },
  },
  [GenerateAthComponentType.TIMEPICKER]: {
    componentName: GenerateAthComponentType.TIMEPICKER,
    title: 'dj-时间选择',
    props: {
      dslInfo: {
        id: '',
        type: GenerateAthComponentType.TIMEPICKER,
        headerName: '时间选择',
        placeholder: 'hhmmss',
        schema: '',
        path: '',
        disabled: false,
        editable: true,
        isFocusDisplay: false,
        fieldType: 'string',
        dataType: AthenaDataType.TIME,
        tooltipMode: 'normal', // 有注释iconType的都要加
        iconType: 'wenhao',
        rules: { value: [] },
        lang: {
          headerName: {
            zh_CN: '时间选择',
            zh_TW: '時間選擇',
            en_US: 'TimePicker',
          },
          placeholder: { zh_CN: 'hhmmss', zh_TW: 'hhmmss', en_US: 'hhmmss' },
          extraContent: {
            zh_CN: '',
            zh_TW: '',
            en_US: '',
          },
          tooltipTitle: {
            zh_CN: '',
            zh_TW: '',
            en_US: '',
          },
        },
      },
    },
  },
  [GenerateAthComponentType.TABLE_GROUP]: {
    ignore: true,
    componentName: GenerateAthComponentType.TABLE_GROUP,
    props: {
      dslInfo: {
        id: '',
        headerName: 'ath表格分组',
        width: 160,
        path: '',
        level: 0,
        suppressFillHandle: false,
        lang: {
          headerName: {
            zh_CN: 'ath表格分组',
            zh_TW: 'ath表格分组',
            en_US: 'ath表格分组',
          },
        },
      },
    },
  },
  [GenerateAthComponentType.DYNAMIC_OPERATION]: {
    componentName: GenerateAthComponentType.DYNAMIC_OPERATION,
    children: [],
    title: '动态操作',
    props: {
      dslInfo: {
        id: '',
        type: GenerateAthComponentType.DYNAMIC_OPERATION,
        select: 'slot-top-right',
        title: '工具栏',
        height: 140,
        group: [],
      },
    },
  },
};
